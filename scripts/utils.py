import difflib
import logging
import os
from datetime import datetime

import hvac
import ruamel.yaml
from colorama import Back, Fore, Style, init
from jinja2 import Template
import glob
import pprint


logger = logging.getLogger(__name__)


def get_vault_client(env):
    client = hvac.Client(url=f"https://vault-ui-{env}.grofer.io")
    client.auth.github.login(token=os.environ.get("VAULT_TOKEN"))
    if not client.is_authenticated():
        raise ValueError("Could not authenticate to Vault")
    return client

def fetch_credentials(env):
    vault_client = get_vault_client(env)
    return vault_client.read("dse/services/jupyterhub/{}".format(env))["data"]

def get_service_account(docker_env, env):
    vault_client = get_vault_client(env)
    mapping = vault_client.read("dse/services/jupyterhub/serviceaccount/{}".format(env))["data"]
    return mapping.get(docker_env, mapping.get('default', 'default'))

def color_diff(diff):
    for line in diff:
        if line.startswith('+'):
            yield Fore.GREEN + line + Fore.RESET
        elif line.startswith('-'):
            yield Fore.RED + line + Fore.RESET
        elif line.startswith('^'):
            yield Fore.BLUE + line + Fore.RESET
        else:
            yield line

def generate_profiles(environment, docker_repo='************.dkr.ecr.ap-southeast-1.amazonaws.com/data/jhub'):
    profiles = []

    for env_dir in glob.glob("./docker/*"):
        docker_env = env_dir.split("/")[-1]
        metafile = os.path.join(env_dir, "meta.yml")

        # https://stackoverflow.com/a/********
        with open(metafile, "r") as f:
            meta = ruamel.yaml.round_trip_load(f, preserve_quotes=True)

        environments = meta.get('environments', [])

        if environment not in environments:
            continue

        display_order = meta.pop("display_order")

        docker_tag = "stable" if environment=="prod" else "nightly"
        docker_image = f"{docker_repo}/{docker_env}:{docker_tag}"

        if not meta["kubespawner_override"].get("image"):
            meta["kubespawner_override"]["image"] = docker_image
        
        meta["serviceAccountName"] = get_service_account(docker_env, environment)

        profiles.append((display_order, meta))
    profile_list = [p[1] for p in sorted(profiles, key=lambda x: x[0])]
    if not any([x.get('default', None) for x in profile_list]):
        profile_list[0]['default'] = True
    return profile_list

def generate_config_and_take_backup(template_file_path, output_file_path, backup_folder, credentials, add_profiles=False):
    backup_file = None
    print(f"Starting with {template_file_path}")
    current_directory = os.path.abspath(os.getcwd())

    if not os.path.isdir(backup_folder):
        os.makedirs(backup_folder)

    with open(template_file_path, "r") as f:
        t = Template(f.read())
        helm_config_str = t.render(**credentials)

    helm_config = ruamel.yaml.round_trip_load(helm_config_str, preserve_quotes=True)
    if add_profiles:
        profile_list = generate_profiles(credentials.get('env'))
        helm_config['singleuser']['profileList'] = profile_list

    if os.path.exists(output_file_path):
        now = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name, extension = os.path.splitext(output_file_path)
        backup_file = f"{backup_folder}/{file_name}.{now}{extension}"
        logger.info(f"Overwriting existing config. Taking backup : {backup_file}")
        os.makedirs(os.path.dirname(os.path.join(current_directory, backup_file)), exist_ok=True)
        os.rename(output_file_path, backup_file)

    os.makedirs(os.path.dirname(os.path.join(current_directory, output_file_path)), exist_ok=True)
    with open(output_file_path, "w+") as f:
        ruamel.yaml.round_trip_dump(helm_config, f)

    if backup_file:
        with open(output_file_path, "r") as f:
            with open(backup_file, "r") as f2:
                diff = difflib.unified_diff(f2.readlines(), f.readlines(), fromfile=backup_file, tofile=output_file_path)
                diff = list(color_diff(diff))
                if diff:
                    logger.info("".join(diff))
                else:
                    logger.info("No changes detected")
