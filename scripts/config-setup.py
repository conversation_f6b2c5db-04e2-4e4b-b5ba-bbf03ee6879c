# -*- coding: utf-8 -*-

import click
import logging
from utils import (
    fetch_credentials,
    generate_config_and_take_backup,
)

logging.basicConfig(format='%(message)s', level=logging.INFO)
logger = logging.getLogger(__name__)


@click.command()
@click.option('--env', type=click.Choice(['prod', 'stage']), help='Infra environment', required=True)
def generate_config(env):
    credentials = fetch_credentials(env)
    namespace = "jhub"
    config = {}
    config["env"] = env
    config["namespace"] = namespace
    config["jupyter_base_url"] = f"https://jhub-{env}.grofer.io"
    config["service_account_name"] = f"blinkit-{env}-jhub-primary-eks-role"

    if env == "stage":
        config["kubeContext"] = "stage-ore"
        config["service_account_arn"] = "arn:aws:iam::************:role/blinkit-stage-jhub-primary-eks-role"
    elif env == "prod":
        config["kubeContext"] = "prod-sgp"
        config["service_account_arn"] = "arn:aws:iam::************:role/blinkit-prod-jhub-primary-eks-role"

    generate_config_and_take_backup(
        template_file_path=f"templates/values-{env}-{namespace}.yaml.default",
        output_file_path=f"{env}/{namespace}/values.yaml",
        backup_folder="config-backups",
        credentials={**credentials, **config},
        add_profiles=True,
    )

    generate_config_and_take_backup(
        template_file_path=f"templates/skaffold.yaml.default",
        output_file_path=f"skaffold.yaml",
        backup_folder="config-backups",
        credentials={**credentials, **config},
    )

    for config_type in ['common', 'pv', 'pvc', 'service-account']:
        generate_config_and_take_backup(
            template_file_path=f"templates/{config_type}.yaml.default",
            output_file_path=f"{env}/{namespace}/{config_type}.yaml",
            backup_folder="config-backups",
            credentials={**credentials, **config},
        )


if __name__ == "__main__":
    generate_config()
