def loginECR(){
    withAWS(region: 'ap-southeast-1', credentials: 'prod-jenkins-aws-username-password') {
        sh(script: 'aws ecr get-login-password | docker login --username AWS --password-stdin 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com')
  }
}

pipeline {  
  agent any

  options {
    buildDiscarder(logRotator(numToKeepStr: "15", daysToKeepStr: "5"))
    // disableConcurrentBuilds()
    skipStagesAfterUnstable()
  }

  environment {
    HARBOR_REGISTRY_TOKEN = credentials("harbor_registry_token")
    HARBOR_REGISTRY_URL = 'https://442534439095.dkr.ecr.ap-southeast-1.amazonaws.com'
    VAULT_TOKEN = credentials("jenkins-github-token")
    VAULT_AUTH_GITHUB_TOKEN = credentials("jenkins-github-token")
    VAULT_URL = "https://vault-ui-prod.grofer.io"
    SLACK_CHANNEL = "#bl-data-deployments"
    PROJECT = env.JOB_NAME.toLowerCase()
  }

  parameters {
    choice(name: 'stack', choices:['dagger', 'airflow', 'jhub', 'spark'], description: 'dse stack you need to build, eg: dagger, jhub')
    choice(name: 'releaseType', choices:['nightly', 'latest'], description: 'release type to be build, nightly, stable, latest')
    string(name: 'artifacts', defaultValue: '.', description: 'building selected artifacts for chosen stack, comma-seperated values, leave as is is for all')
    choice(name: 'concurrency', choices:['1', '2', '3', '4', '5'], description: 'Concurrent builds that can be made')
    booleanParam(name: 'tagToStable', defaultValue: false, description: 'image will be retagged to stable')
    booleanParam(name: 'runHooks', defaultValue: true, description: 'whether hooks of skaffold should be triggered or not (useful incase of first time builds of new projects to avoid hook failures)')
  }

  stages {
    stage("Docker Login") {
      steps {
        loginECR()
      }
    }

    stage("Building images") {
        when { triggeredBy cause: "UserIdCause" }
        steps {
            dir("images/${stack}"){
              sh(script: "skaffold build -p ${releaseType} --cache-artifacts=false -b ${artifacts} --build-concurrency=${concurrency}")
            }
        }
    }
  }
  post {
    success {
      script {
        if (env.BRANCH_NAME == "master") {
          slackSend(botUser: true, tokenCredentialId: "jenkins-app-token", color: "good", message: "[${PROJECT}]: pipeline ${currentBuild.fullDisplayName} completed successfully. See ${env.RUN_DISPLAY_URL} for details.", channel: "$SLACK_CHANNEL")
        }
      }
    }

    failure {
      script {
        if (env.BRANCH_NAME == "master") {
          slackSend(botUser: true, tokenCredentialId: "jenkins-app-token", color: "danger", message: "[${PROJECT}]: pipeline ${currentBuild.fullDisplayName} failed. Please contact data on call. See ${env.RUN_DISPLAY_URL} for details.", channel: "#bl-data-alerts-p0")
        }
      }
    }
  }
}
