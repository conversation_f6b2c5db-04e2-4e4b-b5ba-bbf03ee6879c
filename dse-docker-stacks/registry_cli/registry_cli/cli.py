import click

from .retag import retag
from .add_label_image import add_label_image
from .models.registry_factory import RegistryFactory


@click.group(help="registry_cli is used to add label, tag and deploy an image")
@click.option('--registry-host', type=str, default='HARBOR_REGISTRY', required=True)
@click.pass_context
def entry_point(ctx, registry_host):
    ctx.obj = dict()
    ctx.obj['registry'] = RegistryFactory.create(registry_host)


entry_point.add_command(retag)
entry_point.add_command(add_label_image)
