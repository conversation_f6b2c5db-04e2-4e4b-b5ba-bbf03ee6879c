import click
from datetime import datetime


@click.command("retag", help="Utilities to retag an image")
@click.option('--repo-name', type=str, required=True)
@click.option('--old-tag', type=str, required=True)
@click.option('--new-tag', type=str)
@click.pass_context
def retag(ctx, repo_name, old_tag, new_tag):
    if not new_tag:
        now = datetime.now()
        new_tag = f"{old_tag}-{now.strftime('%Y%m%d')}"
    ctx.obj['registry'].retag(
        repo_name=repo_name,
        old_tag=old_tag,
        new_tag=new_tag
    )
