import json
from .registry import Registry

class Harbor(Registry):
    def __init__(self, http_connection, registry_url):
        super().__init__(http_connection)
        self.registry_url = registry_url

    def retag(self, repo_name, old_tag, new_tag):
        url = f"{self.registry_url}/api/repositories/{repo_name}/tags"
        data = {
            "tag": f"{new_tag}",
            "src_image": f"{repo_name}:{old_tag}",
            "override": True
        }
        try:
            response = self.http_connection.post(url, data=json.dumps(data))
        except Exception as e:
            raise Exception(e)
        if response.status_code == 200:
            print(f"Retagged image {data['src_image']} to {repo_name}:{new_tag} successfully.")
        elif response.status_code == 400:
            raise Exception(f"Invalid image values: {data['src_image']} provided.")
        elif response.status_code == 401:
            raise Exception(f"User has no permission to the source project or destination project {repo_name}.")
        elif response.status_code == 404:
            raise Exception(f"Project or repository {repo_name} not found.")
        elif response.status_code == 409:
            raise Exception(f"Target tag {new_tag} already exists.")
        elif response.status_code == 500:
            raise Exception("Unexpected internal errors.")
        else:
            raise Exception(f"Failed to retag image {data['src_image']} using API {url}")


    def add_label_image(self, repo_name, tag, commit_id):
        url = f"{self.registry_url}/api/repositories/{repo_name}/tags/{tag}/labels"
        data = {
            "id": commit_id
        }
        try:
            response = self.http_connection.post(url, data=json.dumps(data))
        except Exception as e:
            raise Exception(e)
        if response.status_code == 200:
            print(f"label {commit_id} added to image {repo_name}:{tag} successfully.")
        elif response.status_code == 401:
            raise Exception(f"User has no permission to the source project or destination project {repo_name}.")
        elif response.status_code == 403:
            raise Exception(f"Forbidden. User should have write permission for the image {repo_name}:{tag} to perform the action.")
        elif response.status_code == 404:
            raise Exception(f"Project or repository {repo_name} not found.")
        else:
            raise Exception(f"Failed to add label {commit_id} to image {repo_name}:{tag} using API {url}")
