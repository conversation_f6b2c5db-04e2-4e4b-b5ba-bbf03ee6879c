import os
import requests
from .harbor_registry import Harbor


class RegistryFactory:
    def __init__(self):
        pass

    @classmethod
    def create(cls, registry_host):
        session = requests.Session()
        if registry_host == "HARBOR_REGISTRY":
            session.headers.update(
                {
                    "Content-Type": "application/json",
                    "authorization": f"Basic {os.environ.get('HARBOR_REGISTRY_TOKEN')}"
                }
            )
            req_registry = Harbor(
                http_connection=session,
                registry_url=os.environ.get('HARBOR_REGISTRY_URL')
            )
        else:
            raise Exception(f"No such registry host: {registry_host}")
        return req_registry
