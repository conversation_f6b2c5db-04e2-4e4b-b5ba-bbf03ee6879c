import click


@click.command("add_label_image", help="Utilities to add label to an image")
@click.option('--repo-name', type=str, required=True)
@click.option('--tag', type=str, required=True)
@click.option('--commit-id', type=str, required=True)
@click.pass_context
def add_label_image(ctx, repo_name, tag, commit_id):
    ctx.obj['registry'].add_label_image(
        repo_name=repo_name,
        tag=tag,
        commit_id=commit_id
    )
