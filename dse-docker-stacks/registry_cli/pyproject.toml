[tool.poetry]
name = "registry_cli"
version = "0.1.0"
description = ""
authors = ["DE Team"]

[tool.poetry.dependencies]
python = "^3.7"
requests = "^2.26.0"
click = "^8.0.3"

[tool.poetry.dev-dependencies]

[[tool.poetry.source]]
name = "grofers_pypi"
url = "https://pypi.grofer.io/simple"

[tool.poetry.scripts]
registry_cli = "registry_cli.cli:entry_point"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
