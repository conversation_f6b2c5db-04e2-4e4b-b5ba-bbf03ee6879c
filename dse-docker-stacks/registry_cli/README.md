# registry-cli

The cli used to manage the images in the registry

## Installation
1. Install registry_cli from dse-docker-stacks repository if not already installed.
    1. Move into the `dse-docker-stacks/registry_cli` folder
        ```bash
          cd registry_cli
        ```
    2. Run the install command
        ```bash
          pip install .
        ```
## Usage

A sample workflow:   

1. Retag an image.
    ```bash
      registry_cli retag --repo-name 'data/test' --old-tag 'latest'
    ```
    Available option:<br>
    1. `--registry_host` [optional]: specify registry where image resides, by default it is `HARBOR_REGISTRY`.
    2. `--repo-name` [required]: path to image in registry.
    3. `--old-tag` [required]: image tag which is to be re-tagged.
    4. `--new-tag` [optional]: new image tag which is to be replaced by `--old-tag`, by default it is `--old-tag-current-datetime`.

2. Add label to an image
    ```bash
      registry_cli add_label_image --repo-name 'data/test' --tag 'latest' --commit-id '6e2f12b6b3eec671a1b8edeae90a7c53276eee33'
    ```
   Available option:<br>
    1. `--registry_host`[optional]: specify registry where image resides, by default it is `HARBOR_REGISTRY`.
    2. `--repo-name` [required]: path to image in registry.
    3. `--tag` [required]: image tag in which label is to be added.
    4. `--commit-id` [required]: label (i.e last commit-id in GitHub) which is to be added.
