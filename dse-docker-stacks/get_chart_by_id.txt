[{"type": "text", "text": "{\n  \"description_columns\": {},\n  \"id\": 6573,\n  \"label_columns\": {\n    \"cache_timeout\": \"Cache Timeout\",\n    \"certification_details\": \"Certification Details\",\n    \"certified_by\": \"Certified By\",\n    \"changed_on_delta_humanized\": \"Changed On Delta Humanized\",\n    \"dashboards.dashboard_title\": \"Dashboards Dashboard Title\",\n    \"dashboards.id\": \"Dashboards Id\",\n    \"dashboards.json_metadata\": \"Dashboards Json Metadata\",\n    \"description\": \"Description\",\n    \"id\": \"Id\",\n    \"is_managed_externally\": \"Is Managed Externally\",\n    \"owners.first_name\": \"Owners First Name\",\n    \"owners.id\": \"Owners Id\",\n    \"owners.last_name\": \"Owners Last Name\",\n    \"params\": \"Params\",\n    \"query_context\": \"Query Context\",\n    \"slice_name\": \"Slice Name\",\n    \"tags.id\": \"Tags Id\",\n    \"tags.name\": \"Tags Name\",\n    \"tags.type\": \"Tags Type\",\n    \"thumbnail_url\": \"Thumbnail Url\",\n    \"url\": \"Url\",\n    \"viz_type\": \"Viz Type\"\n  },\n  \"result\": {\n    \"cache_timeout\": null,\n    \"certification_details\": null,\n    \"certified_by\": null,\n    \"changed_on_delta_humanized\": \"4 hours ago\",\n    \"dashboards\": [\n      {\n        \"dashboard_title\": \"Growth Dashboard\",\n        \"id\": 4295,\n        \"json_metadata\": \"{\\\"chart_configuration\\\": {\\\"6573\\\": {\\\"id\\\": 6573, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6575\\\": {\\\"id\\\": 6575, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6576\\\": {\\\"id\\\": 6576, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6578\\\": {\\\"id\\\": 6578, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6580\\\": {\\\"id\\\": 6580, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6581\\\": {\\\"id\\\": 6581, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6582\\\": {\\\"id\\\": 6582, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6585\\\": {\\\"id\\\": 6585, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6761\\\": {\\\"id\\\": 6761, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6763\\\": {\\\"id\\\": 6763, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6777\\\": {\\\"id\\\": 6777, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6778\\\": {\\\"id\\\": 6778, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6779\\\": {\\\"id\\\": 6779, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6780\\\": {\\\"id\\\": 6780, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6781\\\": {\\\"id\\\": 6781, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6783, 18547, 18548, 18549, 18550, 26391]}}, \\\"6783\\\": {\\\"id\\\": 6783, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 18547, 18548, 18549, 18550, 26391]}}, \\\"18547\\\": {\\\"id\\\": 18547, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18548, 18549, 18550, 26391]}}, \\\"18548\\\": {\\\"id\\\": 18548, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18549, 18550, 26391]}}, \\\"18549\\\": {\\\"id\\\": 18549, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18550, 26391]}}, \\\"18550\\\": {\\\"id\\\": 18550, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 26391]}}, \\\"26391\\\": {\\\"id\\\": 26391, \\\"crossFilters\\\": {\\\"scope\\\": \\\"global\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550]}}}, \\\"global_chart_configuration\\\": {\\\"scope\\\": {\\\"rootPath\\\": [\\\"ROOT_ID\\\"], \\\"excluded\\\": []}, \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6585, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6783, 18547, 18548, 18549, 18550, 26391]}, \\\"color_scheme\\\": \\\"\\\", \\\"refresh_frequency\\\": 0, \\\"expanded_slices\\\": {}, \\\"label_colors\\\": {}, \\\"timed_refresh_immune_slices\\\": [], \\\"cross_filters_enabled\\\": true, \\\"default_filters\\\": \\\"{}\\\", \\\"native_filter_configuration\\\": [{\\\"id\\\": \\\"NATIVE_FILTER-78i2iKMjv\\\", \\\"controlValues\\\": {\\\"sortAscending\\\": true, \\\"enableEmptyFilter\\\": true, \\\"defaultToFirstItem\\\": false, \\\"multiSelect\\\": false, \\\"searchAllOptions\\\": false, \\\"inverseSelection\\\": false}, \\\"name\\\": \\\"City Name\\\", \\\"filterType\\\": \\\"filter_select\\\", \\\"targets\\\": [{\\\"datasetId\\\": 4160, \\\"column\\\": {\\\"name\\\": \\\"city_name\\\"}}], \\\"defaultDataMask\\\": {\\\"extraFormData\\\": {\\\"filters\\\": [{\\\"col\\\": \\\"city_name\\\", \\\"op\\\": \\\"IN\\\", \\\"val\\\": [\\\"Overall\\\"]}]}, \\\"filterState\\\": {\\\"validateMessage\\\": false, \\\"validateStatus\\\": false, \\\"label\\\": \\\"Overall\\\", \\\"value\\\": [\\\"Overall\\\"]}}, \\\"cascadeParentIds\\\": [], \\\"scope\\\": {\\\"rootPath\\\": [\\\"ROOT_ID\\\"], \\\"excluded\\\": []}, \\\"type\\\": \\\"NATIVE_FILTER\\\", \\\"description\\\": \\\"\\\", \\\"chartsInScope\\\": [6573, 6575, 6576, 6578, 6580, 6581, 6582, 6584, 6585, 6586, 6587, 6761, 6763, 6777, 6778, 6779, 6780, 6781, 6782, 6783, 6784, 6785], \\\"tabsInScope\\\": [\\\"TAB-01FdWUGCo\\\", \\\"TAB-2RNu12K5T\\\", \\\"TAB-3lv3Qq8kV\\\", \\\"TAB-7lNipDU5e\\\", \\\"TAB-H3UCLw4pJ\\\", \\\"TAB-H7wZW_Z9c\\\", \\\"TAB-UfAQtBPQ-9\\\", \\\"TAB-cLCuqdW2H\\\", \\\"TAB-ffUF08-qS\\\", \\\"TAB-qV8GPeM7o\\\", \\\"TAB-qWntKG103\\\"]}], \\\"filter_bar_orientation\\\": \\\"HORIZONTAL\\\", \\\"shared_label_colors\\\": {\\\"NSA - OND '24\\\": \\\"#1FA8C9\\\", \\\"NSA - JFM '25\\\": \\\"#666666\\\", \\\"NSA - JAS '25\\\": \\\"#A868B7\\\", \\\"NSA - AMJ '25\\\": \\\"#ACE1C4\\\", \\\"Buffer Impact - OND '24\\\": \\\"#B2B2B2\\\", \\\"Buffer Impact - JFM '25\\\": \\\"#9EE5E5\\\", \\\"Buffer Impact - JAS '25\\\": \\\"#FCC700\\\", \\\"Buffer Impact - AMJ '25\\\": \\\"#FDE380\\\", \\\"BAU\\\": \\\"#3CCCCB\\\"}, \\\"color_scheme_domain\\\": []}\"\n      }\n    ],\n    \"description\": null,\n    \"id\": 6573,\n    \"is_managed_externally\": false,\n    \"owners\": [\n      {\n        \"first_name\": \"Himanchal\",\n        \"id\": 5155,\n        \"last_name\": \"Chandra\"\n      }\n    ],\n    \"params\": \"{\\\"datasource\\\":\\\"4160__table\\\",\\\"viz_type\\\":\\\"pivot_table_v2\\\",\\\"slice_id\\\":6573,\\\"groupbyColumns\\\":[\\\"month\\\"],\\\"groupbyRows\\\":[\\\"net_category\\\"],\\\"temporal_columns_lookup\\\":{},\\\"metrics\\\":[{\\\"expressionType\\\":\\\"SIMPLE\\\",\\\"column\\\":{\\\"advanced_data_type\\\":null,\\\"certification_details\\\":null,\\\"certified_by\\\":null,\\\"column_name\\\":\\\"dau\\\",\\\"description\\\":null,\\\"expression\\\":null,\\\"filterable\\\":true,\\\"groupby\\\":true,\\\"id\\\":17887,\\\"is_certified\\\":false,\\\"is_dttm\\\":false,\\\"python_date_format\\\":null,\\\"type\\\":\\\"DOUBLE\\\",\\\"type_generic\\\":0,\\\"verbose_name\\\":null,\\\"warning_markdown\\\":null},\\\"aggregate\\\":\\\"SUM\\\",\\\"sqlExpression\\\":null,\\\"datasourceWarning\\\":false,\\\"hasCustomLabel\\\":true,\\\"label\\\":\\\"DAU\\\",\\\"optionName\\\":\\\"metric_foz9u8v2mj_ytnouquuowa\\\"}],\\\"metricsLayout\\\":\\\"COLUMNS\\\",\\\"adhoc_filters\\\":[],\\\"row_limit\\\":10000,\\\"series_limit_metric\\\":{\\\"expressionType\\\":\\\"SIMPLE\\\",\\\"column\\\":{\\\"advanced_data_type\\\":null,\\\"certification_details\\\":null,\\\"certified_by\\\":null,\\\"column_name\\\":\\\"net_category\\\",\\\"description\\\":null,\\\"expression\\\":null,\\\"filterable\\\":true,\\\"groupby\\\":true,\\\"id\\\":17885,\\\"is_certified\\\":false,\\\"is_dttm\\\":false,\\\"python_date_format\\\":null,\\\"type\\\":\\\"VARCHAR\\\",\\\"type_generic\\\":1,\\\"verbose_name\\\":null,\\\"warning_markdown\\\":null},\\\"aggregate\\\":\\\"MAX\\\",\\\"sqlExpression\\\":null,\\\"datasourceWarning\\\":false,\\\"hasCustomLabel\\\":false,\\\"label\\\":\\\"MAX(net_category)\\\",\\\"optionName\\\":\\\"metric_7vs003sormt_54zu866armo\\\"},\\\"order_desc\\\":false,\\\"aggregateFunction\\\":\\\"Average\\\",\\\"rowTotals\\\":false,\\\"rowSubTotals\\\":false,\\\"colTotals\\\":false,\\\"isTotalAtTop\\\":false,\\\"colSubTotals\\\":false,\\\"transposePivot\\\":false,\\\"combineMetric\\\":false,\\\"showRowPercentile\\\":false,\\\"showColPercentile\\\":false,\\\"valueFormat\\\":\\\"INDIAN_CURRENCY\\\",\\\"col_value_formatting\\\":[],\\\"date_format\\\":\\\"smart_date\\\",\\\"rowOrder\\\":\\\"none\\\",\\\"colOrder\\\":\\\"key_a_to_z\\\",\\\"conditional_formatting\\\":[{\\\"column\\\":\\\"DAU\\\",\\\"colorScheme\\\":\\\"#439066\\\",\\\"operator\\\":\\\"None\\\"}],\\\"conditionalCellFormatter\\\":false,\\\"prefix_formatting\\\":[],\\\"suffix_formatting\\\":[],\\\"addBorder\\\":true,\\\"extra_form_data\\\":{},\\\"dashboards\\\":[4295]}\",\n    \"query_context\": \"{\\\"datasource\\\":{\\\"id\\\":4160,\\\"type\\\":\\\"table\\\"},\\\"force\\\":false,\\\"queries\\\":[{\\\"filters\\\":[],\\\"extras\\\":{\\\"having\\\":\\\"\\\",\\\"where\\\":\\\"\\\"},\\\"applied_time_extras\\\":{},\\\"columns\\\":[\\\"month\\\",\\\"net_category\\\"],\\\"metrics\\\":[{\\\"expressionType\\\":\\\"SIMPLE\\\",\\\"column\\\":{\\\"advanced_data_type\\\":null,\\\"certification_details\\\":null,\\\"certified_by\\\":null,\\\"column_name\\\":\\\"dau\\\",\\\"description\\\":null,\\\"expression\\\":null,\\\"filterable\\\":true,\\\"groupby\\\":true,\\\"id\\\":17887,\\\"is_certified\\\":false,\\\"is_dttm\\\":false,\\\"python_date_format\\\":null,\\\"type\\\":\\\"DOUBLE\\\",\\\"type_generic\\\":0,\\\"verbose_name\\\":null,\\\"warning_markdown\\\":null},\\\"aggregate\\\":\\\"SUM\\\",\\\"sqlExpression\\\":null,\\\"datasourceWarning\\\":false,\\\"hasCustomLabel\\\":true,\\\"label\\\":\\\"DAU\\\",\\\"optionName\\\":\\\"metric_foz9u8v2mj_ytnouquuowa\\\"}],\\\"orderby\\\":[[{\\\"expressionType\\\":\\\"SIMPLE\\\",\\\"column\\\":{\\\"advanced_data_type\\\":null,\\\"certification_details\\\":null,\\\"certified_by\\\":null,\\\"column_name\\\":\\\"net_category\\\",\\\"description\\\":null,\\\"expression\\\":null,\\\"filterable\\\":true,\\\"groupby\\\":true,\\\"id\\\":17885,\\\"is_certified\\\":false,\\\"is_dttm\\\":false,\\\"python_date_format\\\":null,\\\"type\\\":\\\"VARCHAR\\\",\\\"type_generic\\\":1,\\\"verbose_name\\\":null,\\\"warning_markdown\\\":null},\\\"aggregate\\\":\\\"MAX\\\",\\\"sqlExpression\\\":null,\\\"datasourceWarning\\\":false,\\\"hasCustomLabel\\\":false,\\\"label\\\":\\\"MAX(net_category)\\\",\\\"optionName\\\":\\\"metric_7vs003sormt_54zu866armo\\\"},true]],\\\"annotation_layers\\\":[],\\\"row_limit\\\":10000,\\\"series_limit\\\":0,\\\"series_limit_metric\\\":{\\\"expressionType\\\":\\\"SIMPLE\\\",\\\"column\\\":{\\\"advanced_data_type\\\":null,\\\"certification_details\\\":null,\\\"certified_by\\\":null,\\\"column_name\\\":\\\"net_category\\\",\\\"description\\\":null,\\\"expression\\\":null,\\\"filterable\\\":true,\\\"groupby\\\":true,\\\"id\\\":17885,\\\"is_certified\\\":false,\\\"is_dttm\\\":false,\\\"python_date_format\\\":null,\\\"type\\\":\\\"VARCHAR\\\",\\\"type_generic\\\":1,\\\"verbose_name\\\":null,\\\"warning_markdown\\\":null},\\\"aggregate\\\":\\\"MAX\\\",\\\"sqlExpression\\\":null,\\\"datasourceWarning\\\":false,\\\"hasCustomLabel\\\":false,\\\"label\\\":\\\"MAX(net_category)\\\",\\\"optionName\\\":\\\"metric_7vs003sormt_54zu866armo\\\"},\\\"order_desc\\\":false,\\\"url_params\\\":{},\\\"custom_params\\\":{},\\\"custom_form_data\\\":{}}],\\\"form_data\\\":{\\\"datasource\\\":\\\"4160__table\\\",\\\"viz_type\\\":\\\"pivot_table_v2\\\",\\\"slice_id\\\":6573,\\\"groupbyColumns\\\":[\\\"month\\\"],\\\"groupbyRows\\\":[\\\"net_category\\\"],\\\"temporal_columns_lookup\\\":{},\\\"metrics\\\":[{\\\"expressionType\\\":\\\"SIMPLE\\\",\\\"column\\\":{\\\"advanced_data_type\\\":null,\\\"certification_details\\\":null,\\\"certified_by\\\":null,\\\"column_name\\\":\\\"dau\\\",\\\"description\\\":null,\\\"expression\\\":null,\\\"filterable\\\":true,\\\"groupby\\\":true,\\\"id\\\":17887,\\\"is_certified\\\":false,\\\"is_dttm\\\":false,\\\"python_date_format\\\":null,\\\"type\\\":\\\"DOUBLE\\\",\\\"type_generic\\\":0,\\\"verbose_name\\\":null,\\\"warning_markdown\\\":null},\\\"aggregate\\\":\\\"SUM\\\",\\\"sqlExpression\\\":null,\\\"datasourceWarning\\\":false,\\\"hasCustomLabel\\\":true,\\\"label\\\":\\\"DAU\\\",\\\"optionName\\\":\\\"metric_foz9u8v2mj_ytnouquuowa\\\"}],\\\"metricsLayout\\\":\\\"COLUMNS\\\",\\\"adhoc_filters\\\":[],\\\"row_limit\\\":10000,\\\"series_limit_metric\\\":{\\\"expressionType\\\":\\\"SIMPLE\\\",\\\"column\\\":{\\\"advanced_data_type\\\":null,\\\"certification_details\\\":null,\\\"certified_by\\\":null,\\\"column_name\\\":\\\"net_category\\\",\\\"description\\\":null,\\\"expression\\\":null,\\\"filterable\\\":true,\\\"groupby\\\":true,\\\"id\\\":17885,\\\"is_certified\\\":false,\\\"is_dttm\\\":false,\\\"python_date_format\\\":null,\\\"type\\\":\\\"VARCHAR\\\",\\\"type_generic\\\":1,\\\"verbose_name\\\":null,\\\"warning_markdown\\\":null},\\\"aggregate\\\":\\\"MAX\\\",\\\"sqlExpression\\\":null,\\\"datasourceWarning\\\":false,\\\"hasCustomLabel\\\":false,\\\"label\\\":\\\"MAX(net_category)\\\",\\\"optionName\\\":\\\"metric_7vs003sormt_54zu866armo\\\"},\\\"order_desc\\\":false,\\\"aggregateFunction\\\":\\\"Average\\\",\\\"rowTotals\\\":false,\\\"rowSubTotals\\\":false,\\\"colTotals\\\":false,\\\"isTotalAtTop\\\":false,\\\"colSubTotals\\\":false,\\\"transposePivot\\\":false,\\\"combineMetric\\\":false,\\\"showRowPercentile\\\":false,\\\"showColPercentile\\\":false,\\\"valueFormat\\\":\\\"INDIAN_CURRENCY\\\",\\\"col_value_formatting\\\":[],\\\"date_format\\\":\\\"smart_date\\\",\\\"rowOrder\\\":\\\"none\\\",\\\"colOrder\\\":\\\"key_a_to_z\\\",\\\"conditional_formatting\\\":[{\\\"column\\\":\\\"DAU\\\",\\\"colorScheme\\\":\\\"#439066\\\",\\\"operator\\\":\\\"None\\\"}],\\\"conditionalCellFormatter\\\":false,\\\"prefix_formatting\\\":[],\\\"suffix_formatting\\\":[],\\\"addBorder\\\":true,\\\"extra_form_data\\\":{},\\\"dashboards\\\":[4295],\\\"force\\\":false,\\\"result_format\\\":\\\"json\\\",\\\"result_type\\\":\\\"full\\\"},\\\"result_format\\\":\\\"json\\\",\\\"result_type\\\":\\\"full\\\"}\",\n    \"slice_name\": \"DAU\",\n    \"tags\": [\n      {\n        \"id\": 2344,\n        \"name\": \"owner:5155\",\n        \"type\": 3\n      },\n      {\n        \"id\": 3,\n        \"name\": \"type:chart\",\n        \"type\": 2\n      }\n    ],\n    \"thumbnail_url\": \"/api/v1/chart/6573/thumbnail/bd1e461f03fd9cde2776378431fe3a33/\",\n    \"url\": \"/explore/?slice_id=6573\",\n    \"viz_type\": \"pivot_table_v2\"\n  },\n  \"show_columns\": [\n    \"cache_timeout\",\n    \"certified_by\",\n    \"certification_details\",\n    \"changed_on_delta_humanized\",\n    \"dashboards.dashboard_title\",\n    \"dashboards.id\",\n    \"dashboards.json_metadata\",\n    \"description\",\n    \"id\",\n    \"owners.first_name\",\n    \"owners.id\",\n    \"owners.last_name\",\n    \"dashboards.id\",\n    \"dashboards.dashboard_title\",\n    \"params\",\n    \"slice_name\",\n    \"thumbnail_url\",\n    \"url\",\n    \"viz_type\",\n    \"query_context\",\n    \"is_managed_externally\",\n    \"tags.id\",\n    \"tags.name\",\n    \"tags.type\"\n  ],\n  \"show_title\": \"Show Slice\"\n}", "uuid": "48a0c1f5-c924-4a47-99e0-92dc09aa15f4"}]