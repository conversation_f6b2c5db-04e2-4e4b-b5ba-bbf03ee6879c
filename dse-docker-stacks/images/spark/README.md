# Spark Docker Stacks

### Base Images

Base images for Spark and PySpark need to be built separetly (as in without skaffold), this is because..

* We build Spark from the source to use specific versions of Spark and Hadoop that are compatible with our services.
* Spark comes with an image builder tool that automatically tags and pushes the images. Thus, using skaffold over the build tool would only be adding a non essential layer that doesn't control anything.


To build base images for Spark and PySpark:

* Make sure you have `java` and `javac` installed, current version of production images were built with `openjdk-1.8.0_292` and `javac-1.8.0_292`.
* Make sure you have docker installed.

First cd to the base directory `cd ./base`, then you need to run the `spark_builder.sh` script as follows:
```.sh
# Syntax:
# sh ./spark_builder.sh [repo] [tag] [push]


# build nightly images of Spark and PySpark
#
# 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/spark:nightly
# 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/spark-py:nightly
#
$ sh ./spark_builder.sh 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data nightly


# Builds the Spark and PySpark images as mentioned above and
# pushes them to the registry specified.
#
$ sh ./spark_builder.sh 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data nightly true
```
The scripts can also be run in a large EC2 machine to speed up build times, you just need java, javac and docker to be installed correctly.


### Derived Images
To build other images like `Sparkle` that use Spark or PySpark as their base image, use skaffold.

latest build
```
skaffold build -p latest --cache-artifacts=false
```

nightly build
```
skaffold build -p nightly --cache-artifacts=false
```
