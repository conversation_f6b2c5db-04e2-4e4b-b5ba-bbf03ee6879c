ARG BASE_IMAGE
FROM $BASE_IMAGE

ARG IMAGE_TYPE
ARG GITHUB_TOKEN
ARG SPARK_UID=185

USER root

RUN apt-get --allow-releaseinfo-change update && apt-get install -y \
    git \
    default-libmysqlclient-dev \
    libpq-dev \
    gnupg \
    htop \
    less

COPY requirements.$IMAGE_TYPE.txt /tmp/requirements.txt
RUN pip install --index-url https://pypi.grofer.io/simple --no-cache-dir -r /tmp/requirements.txt

COPY jars/*.jar /opt/spark/jars/
COPY scripts/* /opt/spark/work-dir

USER ${SPARK_UID}
