import os
import sys

import papermill as pm


def run_notebook_workflow(input_notebook, output_notebook, date_filter):
    pm.execute_notebook(
        input_notebook,
        output_notebook,
        parameters={
            "cwd": os.path.dirname(input_notebook),
            "date_filter": date_filter,
        },
        progress_bar=False,
        report_mode=True,
    )


if __name__ == "__main__":
    if len(sys.argv) < 3:
        raise ValueError(
            f"Path to the notebook workflow or the output notebook path has not been specified."
        )

    input_notebook = sys.argv[1]
    output_notebook = sys.argv[2]

    # TODO: find better ways to pass arguments to notebooks
    date_filter = sys.argv[3] if len(sys.argv) > 3 else None

    run_notebook_workflow(input_notebook, output_notebook, date_filter)
