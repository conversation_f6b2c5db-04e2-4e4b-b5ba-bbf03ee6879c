# TODO: find better ways to download these jars

BASEDIR=$(dirname "$0")
cd $BASEDIR

HUDI_SPARK_BUNDLE_JAR=https://repo1.maven.org/maven2/org/apache/hudi/hudi-spark3.1.2-bundle_2.12/0.10.1/hudi-spark3.1.2-bundle_2.12-0.10.1.jar
AWS_HADOOP_JAR=https://repo1.maven.org/maven2/org/apache/hadoop/hadoop-aws/2.10.1/hadoop-aws-2.10.1.jar
AWS_JAVA_SDK_BUNDLE_JAR=https://repo1.maven.org/maven2/com/amazonaws/aws-java-sdk-bundle/1.11.271/aws-java-sdk-bundle-1.11.271.jar


curl -O -J $HUDI_SPARK_BUNDLE_JAR
curl -O -J $AWS_HADOOP_JAR
curl -O -J $AWS_JAVA_SDK_BUNDLE_JAR
