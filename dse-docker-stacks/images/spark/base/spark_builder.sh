REPO=$1
TAG=${2:-nightly}
PUSH=${3:-false}


SPARK_VERSION=3.1.2
HADOOP_VERSION=2.10.1   # https://issues.apache.org/jira/browse/HADOOP-17338


SOURCE_URL="https://dlcdn.apache.org/spark/spark-$SPARK_VERSION/spark-$SPARK_VERSION.tgz"
SPARK_SOURCE="spark-$SPARK_VERSION"
SPARK_BUILD=spark-$SPARK_VERSION-bin-hadoop$HADOOP_VERSION 


# Download spark source
curl $SOURCE_URL --output $SPARK_SOURCE.tgz
tar -xvzf $SPARK_SOURCE.tgz


# You need java and javac for the build. Have tested this with openjdk and javac 1.8.0_292
# Relevant docs and issues: 
#   - https://spark.apache.org/docs/3.1.2/building-spark.html
#   - https://issues.apache.org/jira/browse/SPARK-35758
#
# Build spark from source
echo "building spark-$SPARK_VERSION from source with hadoop-$HADOOP_VERSION..."
cd ./$SPARK_SOURCE/
./dev/make-distribution.sh --name hadoop$HADOOP_VERSION --pip --tgz -Phive -Phive-thriftserver -Pkubernetes -Dhadoop.version=$HADOOP_VERSION -Phadoop-2.7
cd ..


# Unzip the build
mv $SPARK_SOURCE/$SPARK_BUILD.tgz .
tar -xvzf $SPARK_BUILD.tgz


# Build the images
cd ./$SPARK_BUILD
echo "building spark and pyspark images..."
./bin/docker-image-tool.sh -r $REPO -t $TAG -p ./kubernetes/dockerfiles/spark/bindings/python/Dockerfile build


# Push the images to registry if needed
if [ $PUSH = true ]
then
    echo "pushing spark and pyspark images to registry..."
    ./bin/docker-image-tool.sh -r $REPO -t $TAG -p ./kubernetes/dockerfiles/spark/bindings/python/Dockerfile push
fi
