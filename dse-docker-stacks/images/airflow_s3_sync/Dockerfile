FROM public.ecr.aws/zomato/amazonlinux:2

# Install required packages, including rsync and git
RUN yum update -y \
    && yum install -y unzip rsync git \
    && curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip" \
    && unzip awscliv2.zip \
    && ./aws/install \
    && yum clean all \
    && rm -rf awscliv2.zip aws

# Define environment variables
ENV S3_BUCKET=blinkit-data-staging
ENV S3_KEY=airflow-dags.zip
ENV BRANCH_NAME=prep
ENV IS_INIT=FALSE
ENV GIT_TOKEN=TOKEN


# Set the working directory
WORKDIR /usr/local/airflow/dags/

# Copy the bash script into the container
COPY sync_dags.sh /sync_dags.sh

# Make the script executable
RUN chmod +x /sync_dags.sh

# Run the script as the container's entry point
ENTRYPOINT ["/sync_dags.sh"]

