#!/bin/bash

# Define variables
S3_BUCKET=${S3_BUCKET:-"blinkit-data-staging"}
S3_KEY=${S3_KEY:-"airflow-dags.zip"}
BRANCH_NAME=${BR<PERSON>CH_NAME:-"master"}
IS_INIT=${IS_INIT:-"TRUE"}
GIT_TOKEN=${GIT_TOKEN:-"TOKEN"}
AIRFLOW_ENV=${AIRFLOW_ENV:-"airflow-dags"}
GIT_REPO="https://$<EMAIL>/grofers/$AIRFLOW_ENV.git"
DAGS_DIR="/usr/local/airflow/dags"
ZIP_FILE="$DAGS_DIR/file.zip"
REPO_DIR="$DAGS_DIR/repo"
TEMP_REPO_DIR="$DAGS_DIR/$AIRFLOW_ENV"

# Function to handle GitSync fallback
git_sync_fallback() {
    echo "Fallback to GitSync"
    rm -rf "$TEMP_REPO_DIR"
    git clone -b "$BRANCH_NAME" "$GIT_REPO" "$TEMP_REPO_DIR" || { echo "GitSync failed"; exit 1; }

}

# Function to sync from S3 and handle errors
sync_from_s3() {
    local fallback_triggered=false

    echo "Attempting to download from S3"
    if ! aws s3 cp "s3://$S3_BUCKET/$S3_KEY" "$ZIP_FILE"; then
        fallback_triggered=true
        git_sync_fallback
    fi

    if [ "$fallback_triggered" = false ]; then
        echo "Unzipping the file"
        mkdir -p "$DAGS_DIR"
        if ! unzip -o "$ZIP_FILE" -d "$DAGS_DIR"; then
            fallback_triggered=true
            git_sync_fallback
        fi
    fi

    echo "Performing rsync"
    rsync -a --delete "$TEMP_REPO_DIR/" "$REPO_DIR/"
    rm -rf "$ZIP_FILE"
    echo "rsync operation completed"
    
    if [ "$fallback_triggered" = false ]; then
            echo "S3 Sync Successful"
    fi
}


# Main logic
if [ "$IS_INIT" = "TRUE" ]; then
    echo "Initial S3 sync"
    sync_from_s3
    echo "Operation completed once due to IS_INIT being TRUE"
else
    echo "Starting periodic S3 sync"
    while true; do
        sync_from_s3
        echo "Sleeping for 5 minutes..."
        sleep 300
    done
fi
