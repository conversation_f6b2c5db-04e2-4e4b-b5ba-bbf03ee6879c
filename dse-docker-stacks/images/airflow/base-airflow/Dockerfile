# Taking Apache Airflow Image as base Image
ARG BASE_APACHE_IMAGE
FROM $BASE_APACHE_IMAGE

# Image Type/ Image Tag can be nightly(Latest Master build), or Latest(latest generated release) 
ARG IMAGE_TYPE

# Taking Github Token to access dependencies from github
ARG GITHUB_TOKEN

#Flavour is the path to requirements if there are different requirements to be installed within the cluster
ARG FLAVOUR

# Path for Airflow
ARG AIRFLOW_HOME=/usr/local/airflow

#Switching user context to root
USER root

RUN apt-get update && apt-get install -y \
    libsasl2-modules \
    libsasl2-dev \
    g++

#Switching user to airflow as dependencies needed to be installed for airflow user only
USER airflow

#Installing specefic requirements
COPY requirements.$IMAGE_TYPE/${FLAVOUR}.txt /tmp/requirements.txt
RUN pip install --index-url https://pypi.grofer.io/simple --no-cache-dir --use-deprecated=legacy-resolver -r /tmp/requirements.txt

#Installing Base Requirements
COPY requirements.txt /tmp/requirements.txt
RUN pip install --index-url https://pypi.grofer.io/simple --no-cache-dir --use-deprecated=legacy-resolver -r /tmp/requirements.txt

WORKDIR ${AIRFLOW_HOME}

#Switching user context to root
USER root

#Adding public keys
RUN apt-key adv --keyserver keyserver.ubuntu.com --recv-keys 467B942D3A79BD29
