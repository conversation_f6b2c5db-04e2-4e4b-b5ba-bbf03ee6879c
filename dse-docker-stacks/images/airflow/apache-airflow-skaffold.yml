apiVersion: skaffold/v2beta26
kind: Config
profiles:
- name: airflow
  build:
    tagPolicy:
      envTemplate:
        template: 2.3.3-python-3.9
    artifacts:
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/apache-airflow-base
      custom:
        buildCommand: ./apache-airflow-build.sh -a 2.3.3 -p public.ecr.aws/zomato/python:3.9-slim-bullseye -c 3.9 -g v2.3.3patch1
    local:
      push: true
