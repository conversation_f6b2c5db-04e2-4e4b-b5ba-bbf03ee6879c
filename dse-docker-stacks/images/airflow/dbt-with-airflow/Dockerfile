# Taking Base Airflow Image as base Image, which include apache airflow + pencilbox + decore + some additional dependencies
ARG BASE_AIRFLOW
FROM $BASE_AIRFLOW

# Image Type/ Image Tag can be nightly(Latest Master build), or Latest(latest generated release) 
ARG IMAGE_TYPE
# Taking Github Token to access dependencies from github
ARG GITHUB_TOKEN

# Flavour is the path to requirements if there are different requirements to be installed within the cluster
ARG FLAVOUR

#Switching user to airflow as dependencies needed to be installed for airflow user only
USER airflow

# Create VirtualEnv for installing dbt
RUN python3 -m venv /home/<USER>/dbt/dbt-core

#To eliminate ERROR: Can not perform a '--user' install. User site-packages are not visible in this virtualenv.
RUN sed -i 's/include-system-site-packages = false/include-system-site-packages = true/' /home/<USER>/dbt/dbt-core/pyvenv.cfg

# Patch protobuf bug
RUN sed -i 's/and field.has_presence/and getattr(field, "has_presence", False)/' $(python -c "import google.protobuf.json_format as j; print(j.__file__)")

# Install DBT Requirements
COPY requirements.$IMAGE_TYPE/dbt.txt /tmp/requirements.txt
RUN /home/<USER>/dbt/dbt-core/bin/pip install --no-cache-dir -r /tmp/requirements.txt

#changing directory to airflow home
WORKDIR ${AIRFLOW_HOME}

#Switching user to again root
USER root
