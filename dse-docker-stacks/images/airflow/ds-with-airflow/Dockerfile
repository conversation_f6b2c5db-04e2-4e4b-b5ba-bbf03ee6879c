# Taking Base Airflow Image as base Image, which include apache airflow + pencilbox + decore + some additional dependencies
ARG BASE_AIRFLOW
FROM $BASE_AIRFLOW

# Image Type/ Image Tag can be nightly(Latest Master build), or Latest(latest generated release) 
ARG IMAGE_TYPE

# Taking Github Token to access dependencies from github
ARG GITHUB_TOKEN

#Flavour is the path to requirements if there are different requirements to be installed within the cluster
ARG FLAVOUR

#Switching user context to root
USER root

# Installing Additional Dependenies required for Orbis build
RUN apt-get update && apt-get install -y \
  default-libmysqlclient-dev \
  libpq-dev \
  python3-rtree \
  libgdal-dev \
  openssh-server\
  htop\
  cmake\
  make\
  vim\
  gcc\
  libtool\
  zsh

RUN python -m pip install --upgrade pip

#Installing nbgitpuller which is required for orbis build
RUN pip install --no-cache-dir nbgitpuller cython==0.29.28 numpy==1.24.3 pybind11==2.11.1 fasttext==0.9.2
RUN pip install lightfm==1.17

#Installing Orbis
COPY requirements.$IMAGE_TYPE/${FLAVOUR}.txt /tmp/requirements.txt
RUN pip install --index-url https://pypi.grofer.io/simple --no-cache-dir -r /tmp/requirements.txt
