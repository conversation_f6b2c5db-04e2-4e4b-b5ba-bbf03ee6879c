#!/bin/bash

checkOptions() { echo "Usage: $0 [-a <airflow-version>] [-p <python-base-image>]" 1>&2; exit 1; }

while getopts ":a:p:c:g:" o; do
    case "${o}" in
        a)
            a=${OPTARG}
            ;;
        p)
            p=${OPTARG}
            ;;
        c)
            c=${OPTARG}
            ;;
        g)
            g=${OPTARG}
            ;;
    esac
done
shift $((OPTIND-1))

if [ -z "${a}" ] && [ -z "${p}" ]; then
    checkOptions
fi

AIRFLOW_VERSION=$a
PYTHON_BASE_IMAGE=$p
PYTHON_CONSTRAINTS_VERSION=$c
GROFERS_GITHUB_TAG=$g

if [ -z $PYTHON_CONSTRAINTS_VERSION ]; then
    PYTHON_CONSTRAINTS_VERSION=`echo $PYTHON_BASE_IMAGE | grep -Eo '[0-9]\.[0-9]+'`
fi

echo "Building airflow version:$AIRFLOW_VERSION"
echo "Using python base image as:$PYTHON_BASE_IMAGE"
echo "Using Dockerfile from grofers/airflow.git#$GROFERS_GITHUB_TAG"
echo "Using python constraint version as:$PYTHON_CONSTRAINTS_VERSION"

# Airflow 2.3.0 onwards, they have restricted running pip as root user. To enable running pip as root user we changed the Dockerfile (tag 2.3.1) and re-tagged it as v2.3.1.
# While upgrading Airflow. Please make sure to replicate above changes in the Dockerfile with appropriate tag.
docker build https://github.com/grofers/airflow.git#$GROFERS_GITHUB_TAG --build-arg AIRFLOW_VERSION="$AIRFLOW_VERSION" \
--build-arg AIRFLOW_HOME="/usr/local/airflow" \
--build-arg AIRFLOW_INSTALLATION_METHOD="apache-airflow" \
--build-arg AIRFLOW_CONSTRAINTS_REFERENCE="constraints-$AIRFLOW_VERSION" \
--build-arg ADDITIONAL_RUNTIME_APT_DEPS="libffi7 libffi-dev libssl-dev libpq-dev default-libmysqlclient-dev build-essential git" \
--build-arg AIRFLOW_PIP_VERSION="22.3" \
--build-arg PYTHON_BASE_IMAGE="$PYTHON_BASE_IMAGE" \
--build-arg AIRFLOW_CONSTRAINTS_LOCATION="https://raw.githubusercontent.com/apache/airflow/constraints-$AIRFLOW_VERSION/constraints-$PYTHON_CONSTRAINTS_VERSION.txt" \
--tag $IMAGE

if $PUSH_IMAGE
  then
    docker push $IMAGE
fi
