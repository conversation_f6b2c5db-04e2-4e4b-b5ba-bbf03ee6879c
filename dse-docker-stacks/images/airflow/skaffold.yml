apiVersion: skaffold/v2beta26
kind: Config
.hooks: &default-hooks
  before:
  - command: ["/bin/bash", "-c", "if [ $runHooks = true ]; then MANIFEST=$(aws ecr batch-get-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-ids imageTag=$SKAFFOLD_IMAGE_TAG --output json | jq --raw-output --join-output '.images[0].imageManifest'); echo $MANIFEST > latest_backup.json; aws ecr put-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-tag $SKAFFOLD_IMAGE_TAG-$(date +%Y%m%d%H%M) --image-manifest file://latest_backup.json; fi"]
  after:
  - command: ["/bin/bash", "-c", "if [[ $tagToStable = true && $runHooks = true ]]; then MANIFEST=$(aws ecr batch-get-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-ids imageTag=stable --output json | jq --raw-output --join-output '.images[0].imageManifest'); echo $MANIFEST > stable_backup.json; aws ecr put-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-tag stable-$(date +%Y%m%d%H%M) --image-manifest file://stable_backup.json; fi"]
  - command: ["/bin/bash", "-c", "if [[ $tagToStable = true || $runHooks = true ]]; then MANIFEST=$(aws ecr batch-get-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-ids imageTag=$SKAFFOLD_IMAGE_TAG --output json | jq --raw-output --join-output '.images[0].imageManifest'); echo $MANIFEST > latest_stable.json; aws ecr put-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-tag stable --image-manifest file://latest_stable.json; fi"]
profiles:
- name: nightly
  build:
    tagPolicy:
      envTemplate:
        template: nightly
    artifacts:
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common
      context: ./base-airflow
      docker:
        buildArgs:
          BASE_APACHE_IMAGE: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/apache-airflow-base:2.3.3-python-3.9
          IMAGE_TYPE: nightly
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: default
        noCache: true
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de
      context: ./base-airflow
      docker:
        buildArgs:
          BASE_APACHE_IMAGE: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/apache-airflow-base:2.3.3-python-3.9
          IMAGE_TYPE: nightly
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: de
        noCache: true
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep
      context: ./base-airflow
      docker:
        buildArgs:
          BASE_APACHE_IMAGE: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/apache-airflow-base:2.3.3-python-3.9
          IMAGE_TYPE: nightly
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: prep
        noCache: true
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-ds-common
      context: ./ds-with-airflow
      docker:
        buildArgs:
          IMAGE_TYPE: nightly
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: default
        noCache: true
      requires:
      - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common
        alias: BASE_AIRFLOW
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt
      context: ./dbt-with-airflow
      docker:
        buildArgs:
          IMAGE_TYPE: nightly
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: dbt
        noCache: true
      requires:
      - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de
        alias: BASE_AIRFLOW
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep/dbt
      context: ./dbt-with-airflow
      docker:
        buildArgs:
          IMAGE_TYPE: nightly
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: dbt
        noCache: true
      requires:
      - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep
        alias: BASE_AIRFLOW
    local:
      push: true
- name: latest
  build:
    tagPolicy:
      envTemplate:
        template: latest
    artifacts:
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common
      context: ./base-airflow
      hooks: *default-hooks
      docker:
        buildArgs:
          BASE_APACHE_IMAGE: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/apache-airflow-base:2.3.3-python-3.9
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: default
        noCache: true
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de
      context: ./base-airflow
      hooks: *default-hooks
      docker:
        buildArgs:
          BASE_APACHE_IMAGE: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/apache-airflow-base:2.3.3-python-3.9
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: de
        noCache: true
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep
      context: ./base-airflow
      hooks: *default-hooks
      docker:
        buildArgs:
          BASE_APACHE_IMAGE: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/apache-airflow-base:2.3.3-python-3.9
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: prep
        noCache: true
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-ds-common
      context: ./ds-with-airflow
      hooks: *default-hooks
      docker:
        buildArgs:
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: default
        noCache: true
      requires:
      - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-common
        alias: BASE_AIRFLOW
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de/dbt
      context: ./dbt-with-airflow
      hooks: *default-hooks
      docker:
        buildArgs:
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: dbt
        noCache: true
      requires:
      - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de
        alias: BASE_AIRFLOW
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep/dbt
      context: ./dbt-with-airflow
      hooks: *default-hooks
      docker:
        buildArgs:
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: dbt
        noCache: true
      requires:
      - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-prep
        alias: BASE_AIRFLOW
    local:
      push: true
