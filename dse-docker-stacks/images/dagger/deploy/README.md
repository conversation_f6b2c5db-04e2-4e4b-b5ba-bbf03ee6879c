## Deploy job

Used by <PERSON> pipelines to deploy dags when PRs are merged on `airflow-dags` or `airflow-de-dags`.

```
$ docker run --rm \
    --mount "src=efs,dst=/mnt/efs,volume-opt=device=:/,\"volume-opt=o=addr=*************,nfsvers=4.1,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2,noresvport\",type=volume,volume-driver=local,volume-opt=type=nfs" \
    -e REPO_NAME='airflow-de-dags' \
    -e GITHUB_TOKEN='' \
    -e EFS_DAGS_SUBPATH='airflow-de' \
    -e EFS_BACKUP_SUBPATH='de-dag-backups' \
    -t 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de-dags-deploy:d8040aa944 deploy
```
