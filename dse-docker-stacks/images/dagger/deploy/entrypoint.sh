#!/usr/bin/env bash
# Expects airflow_home already set up
# With master_commit

case "$1" in
  help)
    echo "./entrypoint.sh deploy - Deploy new DAGs"
    echo "./entrypoint.sh restore - Restore old DAGs"
  ;;
  deploy)
    ls -1trd /mnt/efs/$EFS_BACKUP_SUBPATH/* | head -n -5 | xargs rm -rf --

    ts=`date -Ins`
    master_commit=`git ls-remote https://cigrofers:$GITHUB_TOKEN/grofers/$REPO_NAME.git | grep refs/heads/$BRANCH | cut -f 1`

    git clone -b $BRANCH --single-branch https://cigrofers:$<EMAIL>/grofers/$REPO_NAME

    mkdir -p /mnt/efs/$EFS_BACKUP_SUBPATH/$ts/dags
    mkdir -p /mnt/efs/$EFS_BACKUP_SUBPATH/$ts/daggers
    mkdir -p /mnt/efs/$EFS_BACKUP_SUBPATH/$ts/plugins

    cp /mnt/efs/$EFS_DAGS_SUBPATH/master-commit /mnt/efs/$EFS_BACKUP_SUBPATH/$ts/
    cp -r /mnt/efs/$EFS_DAGS_SUBPATH/dags/* /mnt/efs/$EFS_BACKUP_SUBPATH/$ts/dags/
    cp -r /mnt/efs/$EFS_DAGS_SUBPATH/daggers/* /mnt/efs/$EFS_BACKUP_SUBPATH/$ts/daggers/
    cp -r /mnt/efs/$EFS_PLUGINS_SUBPATH/plugins/* /mnt/efs/$EFS_BACKUP_SUBPATH/$ts/plugins/

    echo $master_commit > /mnt/efs/$EFS_DAGS_SUBPATH/master-commit
    rsync -arv --delete $REPO_NAME/dags/ /mnt/efs/$EFS_DAGS_SUBPATH/dags/
    rsync -arv --delete $REPO_NAME/daggers/ /mnt/efs/$EFS_DAGS_SUBPATH/daggers/
    rsync -arv --delete $REPO_NAME/plugins/ /mnt/efs/$EFS_PLUGINS_SUBPATH/plugins/
  ;;
  restore)
    ts=`ls -Art /mnt/efs/$EFS_BACKUP_SUBPATH | tail -n 1`

    cp /mnt/efs/$EFS_BACKUP_SUBPATH/$ts/master-commit /mnt/efs/$EFS_DAGS_SUBPATH/
    rsync -arv --delete /mnt/efs/$EFS_BACKUP_SUBPATH/$ts/dags/ /mnt/efs/$EFS_DAGS_SUBPATH/dags/
    rsync -arv --delete /mnt/efs/$EFS_BACKUP_SUBPATH/$ts/daggers/ /mnt/efs/$EFS_DAGS_SUBPATH/daggers/
    rsync -arv --delete /mnt/efs/$EFS_BACKUP_SUBPATH/$ts/plugins/ /mnt/efs/$EFS_PLUGINS_SUBPATH/plugins/
  ;;
esac
