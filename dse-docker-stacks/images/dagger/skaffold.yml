apiVersion: skaffold/v2beta26
kind: Config
.hooks: &default-hooks
  before:
  - command: ["/bin/bash", "-c", "if [ $runHooks = true ]; then MANIFEST=$(aws ecr batch-get-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-ids imageTag=$SKAFFOLD_IMAGE_TAG --output json | jq --raw-output --join-output '.images[0].imageManifest'); echo $MANIFEST > latest_backup.json; aws ecr put-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-tag $SKAFFOLD_IMAGE_TAG-$(date +%Y%m%d%H%M) --image-manifest file://latest_backup.json; fi"]
  after:
  - command: ["/bin/bash", "-c", "if [[ $tagToStable = true && $runHooks = true ]]; then MANIFEST=$(aws ecr batch-get-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-ids imageTag=stable --output json | jq --raw-output --join-output '.images[0].imageManifest'); echo $MANIFEST > stable_backup.json; aws ecr put-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-tag stable-$(date +%Y%m%d%H%M) --image-manifest file://stable_backup.json; fi"]
  - command: ["/bin/bash", "-c", "if [[ $tagToStable = true || $runHooks = true ]]; then MANIFEST=$(aws ecr batch-get-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-ids imageTag=$SKAFFOLD_IMAGE_TAG --output json | jq --raw-output --join-output '.images[0].imageManifest'); echo $MANIFEST > latest_stable.json; aws ecr put-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-tag stable --image-manifest file://latest_stable.json; fi"]
profiles:
- name: latest
  build:
    tagPolicy:
      envTemplate:
        template: latest
    artifacts:
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de-dags-deploy
      context: ./deploy
      hooks: *default-hooks
      docker:
        buildArgs:
          BASE_IMAGE: public.ecr.aws/zomato/python:3.9-slim
        noCache: true
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de-dags-build
      context: ./build
      hooks: *default-hooks
      docker:
        buildArgs:
          BASE_IMAGE: public.ecr.aws/zomato/python:3.9
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: airflow-de
        noCache: true
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-dags-deploy
      context: ./deploy
      hooks: *default-hooks
      docker:
        buildArgs:
          BASE_IMAGE: public.ecr.aws/zomato/python:3.9-slim
        noCache: true
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-dags-build
      context: ./build
      hooks: *default-hooks
      docker:
        buildArgs:
          BASE_IMAGE: public.ecr.aws/zomato/python:3.9
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: airflow
        noCache: true
    local:
      push: true
- name: nightly
  build:
    tagPolicy:
      envTemplate:
        template: nightly
    artifacts:
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de-dags-deploy
      context: ./deploy
      docker:
        buildArgs:
          BASE_IMAGE: public.ecr.aws/zomato/python:3.9-slim
        noCache: true
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-de-dags-build
      context: ./build
      docker:
        buildArgs:
          BASE_IMAGE: public.ecr.aws/zomato/python:3.9
          IMAGE_TYPE: nightly
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: airflow-de
        noCache: true
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-dags-deploy
      context: ./deploy
      docker:
        buildArgs:
          BASE_IMAGE: public.ecr.aws/zomato/python:3.9-slim
        noCache: true
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/airflow-dags-build
      context: ./build
      docker:
        buildArgs:
          BASE_IMAGE: public.ecr.aws/zomato/python:3.9
          IMAGE_TYPE: nightly
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
          FLAVOUR: airflow
        noCache: true
    local:
      push: true
