ARG BASE_JHUB
FROM $BASE_JHUB

USER root

# [IMP] DON'T install NVIDIA drivers in container - use host drivers

# Install system dependencies 
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    gnupg2 \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install PyTorch with CUDA support that matches host driver
# Host driver 550.163.01 supports CUDA 12.x
RUN pip install --no-cache-dir \
    torch==2.1.0 \
    torchvision==0.16.0 \
    torchaudio==2.1.0 \
    --index-url https://download.pytorch.org/whl/cu121

# Install GPU monitoring tools with available version
RUN pip install --no-cache-dir \
    nvidia-ml-py3==7.352.0 \
    gpustat==1.1.1

USER $NB_UID
