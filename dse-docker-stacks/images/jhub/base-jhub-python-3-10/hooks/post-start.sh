cp /root/.bashrc /home/<USER>/.bashrc
cp /root/.condarc /home/<USER>/.condarc
cp /root/.profile /home/<USER>/.profile
cp /root/README.ipynb /home/<USER>/README.ipynb

CONFIG=/tmp/config.json.tpl
if test -f "$CONFIG"; then
    mkdir -p /home/<USER>/.sparkmagic
fi

envsubst < /tmp/config.json.tpl | cat > /home/<USER>/.sparkmagic/config.json

conda init zsh

mkdir -p /home/<USER>/Documents
mkdir -p /home/<USER>/.conda-envs
mkdir -p /home/<USER>/Development/grofers

if [ ! -L /home/<USER>/Shared ]; then
    ln -s /shared/Shared /home/<USER>/Shared
fi

if [ -d "/opt/spark/jars/" ]; then
    aws s3 cp s3://grofers-test-dse-singapore/bhanu/hudi-spark-bundle.jar /opt/spark/jars/
fi
