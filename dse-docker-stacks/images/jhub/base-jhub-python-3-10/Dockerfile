ARG BASE_JHUB_IMAGE
FROM $BASE_JHUB_IMAGE

USER root

RUN apt-get update && apt-get install -y \
  libmysqlclient-dev \
  libpq-dev \
  libsasl2-modules \
  libsasl2-dev \
  openssh-server \
  gnupg \
  htop \
  less \
  vim \
  zsh \
  g++

RUN pip install --no-cache-dir nbgitpuller awscli==1.37.10 \
 jupyter-resource-usage==0.7.2

COPY config/ssh /root/.ssh/config

COPY config/bash_config /root/bash_config
RUN cat /root/bash_config >> /root/.bashrc

COPY config/conda_config /root/.condarc
COPY config/bash_profile /root/.profile
COPY config/README.ipynb /root/README.ipynb

COPY hooks/post-start.sh /root/post-start.sh
COPY hooks/pre-stop.sh /root/pre-stop.sh

RUN chown -R $NB_UID:$NB_GID $CONDA_DIR /home/<USER>

USER $NB_UID

RUN conda install --quiet --yes \
  'nb_conda_kernels' && \
  conda clean --all -f -y && \
  fix-permissions $CONDA_DIR && \
  fix-permissions /home/<USER>

WORKDIR /home/<USER>

# tenant required for zspark
ENV TENANT=blinkit