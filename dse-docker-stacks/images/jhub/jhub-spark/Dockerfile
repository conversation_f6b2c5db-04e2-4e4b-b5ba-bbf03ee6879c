# Taking Base JHub Image as base Image, which include jupyter/datascience-notebook:lab-4.0.0 + some additional dependencies
ARG BASE_JHUB

FROM public.ecr.aws/zomato/maven:3.8.6-openjdk-11-slim AS maven-builder

# Set the working directory
WORKDIR /app
    
# Copy the pom.xml file to the container
COPY pom.xml .
    
# Run Maven to download dependencies
RUN mvn dependency:copy-dependencies -DoutputDirectory=/app/dependencies

FROM $BASE_JHUB

COPY sparkeks_kernel/ /tmp/sparkeks_kernel/
COPY pyspark/context.py /tmp/pyspark/context.py

# Image Type/ Image Tag can be nightly(Latest Master build), or Latest(latest generated release)
ARG IMAGE_TYPE

# Taking Github Token to access dependencies from github
ARG GITHUB_TOKEN

#Switching user to root as dependencies needed to be installed for root user
USER root

#Installing spark Requirements
COPY requirements.$IMAGE_TYPE/requirements.txt /tmp/requirements.txt

RUN apt-get update && \
    apt-get install -y --no-install-recommends openjdk-17-jdk openjdk-17-jre

RUN pip install pyspark==3.5.1 && \
    jupyter-kernelspec install /tmp/sparkeks_kernel --sys-prefix && \
    cp -r /tmp/sparkeks_kernel /opt/conda/lib/python3.10/site-packages/ && \
    cp /tmp/pyspark/context.py /opt/conda/lib/python3.10/site-packages/pyspark/context.py && \
    mkdir -p /opt/spark && chown -R $NB_USER:$NB_GID /opt/spark

RUN pip install --index-url https://pypi.grofer.io/simple --no-cache-dir -r /tmp/requirements.txt

COPY --from=maven-builder /app/dependencies /opt/spark/jars

COPY pod_template.json /tmp/pod_template.json
RUN chown -R $NB_USER:$NB_GID /tmp/pod_template.json

COPY spark_magic_config.json.tpl /tmp/config.json.tpl

USER $NB_USER