import os
import requests
import json
import re
from typing import Dict, Optional, Any, List
import boto3
import socket
import random
import string

config_dict: Dict[str, Any] = {
    "hive_metastore_uris": {
        "blinkit_hive": "datalake-hive-metastore-service-data.prod-sgp-k8s.grofer.io",
        "blinkit_iceberg": "vpce-06233f70360f757f8-enprgsf3.vpce-svc-0dcaa5761ef958410.ap-southeast-1.vpce.amazonaws.com"
    },
    "kubernetes_ssm_key": {
        "blinkit": "/services/kubernetes/prod/blinkit/spark/jhub/K8S_AUTH_CONFIG"
    },
    "region_env_var":{
        "blinkit": "AWS_DEFAULT_REGION"

    }
}


def get_parameters(parameters: List[str], with_decryption: bool = True, tenant: str = "blinkit") -> Dict[str, Any]:
    region_name = get_region_name(tenant)
    if not parameters:
        return {}
    ssm_client = boto3.client(service_name='ssm', region_name=region_name)

    response = ssm_client.get_parameters(
        Names=parameters,
        WithDecryption=with_decryption
    ).get('Parameters', [])
    return response

def get_tenant():
    tenant = os.environ.get('TENANT', "blinkit")
    return tenant.lower()

def get_region_name(tenant: str):
    return os.environ.get(config_dict["region_env_var"][tenant], "ap-southeast-1")

def get_app_name(username: str, length: int = 8):
    letters = string.ascii_letters
    random_string = ''.join(random.choice(letters) for i in range(length))
    app_name = f"{username}_{random_string}"
    
    return app_name
    
def start_spark_eks_session(shell,ipython_display, session_configurations):

    spark_session_count = shell.db.get("SPARK_SESSION_COUNT",0)
    if spark_session_count >= 2:
        ipython_display.send_error("Max no. of spark session can be 2. Please end existing spark session to start a new spark session")
        return False
    
    username = os.environ.get('JUPYTERHUB_USER', "Unknown_User")
    app_name = get_app_name(username)
    tenant = get_tenant()

    executor_service_account = os.environ.get("BLINKIT_ANALYTICS_AWS_SA_NAME", "prod-blinkit-eks-spark-jhub-role")
    driver_ip = socket.gethostbyname(socket.gethostname())
    
    spark_jars_dir = "/opt/spark/jars"

    jar_list = os.listdir(spark_jars_dir)    #listing all the jars
    spark_jars = ",".join(f"{spark_jars_dir}/{jar}" for jar in jar_list)

    try:
        kubernetes_config = get_parameters(parameters=[config_dict["kubernetes_ssm_key"][tenant]], tenant=tenant)
        kubernetes_config = json.loads(kubernetes_config[0]['Value'])
    except Exception as e:
        ipython_display.send_error(f"Error parsing Kubernetes config from ssm\n Error: {e}\n fetching from vault")
        return

    kubernetes_api_url = kubernetes_config['kubernetes_api_url']
    kubernetes_oauthtoken = kubernetes_config['kubernetes_oauth_token']
    kubernetes_namespace = kubernetes_config['kubernetes_namespace']
    kubernetes_certificate = kubernetes_config['kubernetes_certificate']
    spark_executor_image = os.environ.get("SPARK_EXECUTOR_IMAGE","125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/spark-platform:0.0.2_5d8d18fbf144")


    with open('/tmp/kubernetes_cacert.crt', 'w') as f:
        f.write(kubernetes_certificate)
        f.close()

    extra_configurations= ""


    spark_submit_command = f"""
        from pyspark.sql import SparkSession
        spark = SparkSession.builder.master("k8s://{kubernetes_api_url}:443")\
            .appName("{app_name}")\
            .config("spark.submit.deployMode", "client")\
            .config("spark.driver.host", "{driver_ip}")\
            .config("spark.driver.port", "8002")\
            .config("spark.ui.port","4040")\
            .config("spark.driver.bindAddress", "0.0.0.0")\
            .config("spark.kubernetes.authenticate.caCertFile", "/tmp/kubernetes_cacert.crt")\
            .config("spark.kubernetes.authenticate.oauthToken", "{kubernetes_oauthtoken}")\
            .config("spark.blockManager.port", "8001")\
            .config("spark.kubernetes.namespace", "{kubernetes_namespace}")\
            .config("spark.kubernetes.container.image", "{spark_executor_image}")\
            .config("spark.hadoop.fs.s3a.aws.credentials.provider", "com.amazonaws.auth.WebIdentityTokenCredentialsProvider")\
            .config("spark.kubernetes.authenticate.executor.serviceAccountName", f"{executor_service_account}")\
            .config("spark.hadoop.fs.s3a.assumed.role.credentials.cache", "false")\
            .config("spark.kubernetes.container.image.pullPolicy", "Always")\
            .config("spark.hadoop.fs.s3a.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")\
            .config("spark.hadoop.fs.s3.impl", "org.apache.hadoop.fs.s3a.S3AFileSystem")\
            .config("spark.hadoop.fs.s3a.connection.timeout", "1200000")\
            .config("spark.hadoop.fs.s3a.path.style.access", "true")\
            .config("spark.hadoop.fs.s3a.connection.maximum", "200")\
            .config("spark.hadoop.fs.s3a.fast.upload", "true")\
            .config("spark.hadoop.hive.metastore.uris", "thrift://{config_dict["hive_metastore_uris"]["blinkit_hive"]}:9083")\
            .config("spark.hadoop.hive.execution.engine", "tez")\
            .config("spark.default.parallelism", "50")\
            .config("spark.driver.defaultJavaOptions", "-Divy.cache.dir=/tmp -Divy.home=/tmp -XX,+UseG1GC -XX,+UnlockDiagnosticVMOptions -XX,+G1SummarizeConcMark -XX,InitiatingHeapOccupancyPercent=35 -XX,OnOutOfMemoryError='kill -9 %p'")\
            .config("spark.dynamicAllocation.enabled", "true")\
            .config("spark.executor.defaultJavaOptions", "-XX,+UseG1GC -XX,+UnlockDiagnosticVMOptions -XX,+G1SummarizeConcMark -XX,InitiatingHeapOccupancyPercent=35 -XX,OnOutOfMemoryError='kill -9 %p'")\
            .config("spark.executor.heartbeatInterval", "60s")\
            .config("spark.history.fs.cleaner.enabled", "true")\
            .config("spark.history.fs.cleaner.interval", "1h")\
            .config("spark.history.fs.cleaner.maxAge", "72h")\
            .config("spark.locality.wait", "10")\
            .config("spark.logConf", "true")\
            .config("spark.memory.fraction", "0.80")\
            .config("spark.memory.storageFraction", "0.30")\
            .config("spark.network.timeout", "800s")\
            .config("spark.rdd.compress", "true")\
            .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")\
            .config("spark.shuffle.blockTransferService", "nio")\
            .config("spark.shuffle.compress", "true")\
            .config("spark.shuffle.spill.compress", "true")\
            .config("spark.sql.hive.convertMetastoreOrc", "true")\
            .config("spark.sql.orc.enableVectorizedReader", "true")\
            .config("spark.sql.orc.impl", "native")\
            .config("spark.sql.shuffle.partitions", "50")\
            .config("spark.storage.level", "MEMORY_AND_DISK_SER")\
            .config("spark.yarn.scheduler.reporterThread.maxFailures", "5")\
            .config("spark.yarn.maxAppAttempts", "1")\
            .config("spark.worker.timeout", "1200000")\
            .config("spark.sql.broadcastTimeout", "12000")\
            .config("spark.blacklist.decommissioning.enabled", "true")\
            .config("spark.blacklist.decommissioning.timeout", "1h")\
            .config("spark.decommissioning.timeout.threshold", "20")\
            .config("spark.executor.id", "driver")\
            .config("spark.files.fetchFailure.unRegisterOutputOnHost", "true")\
            .config("spark.hadoop.orc.overwrite.output.file", "true")\
            .config("spark.hadoop.yarn.timeline-service.enabled", "false")\
            .config("spark.sql.catalogImplementation", "hive")\
            .config("spark.sql.parquet.fs.optimized.committer.optimization-enabled", "true")\
            .config("spark.sql.extensions", "org.apache.iceberg.spark.extensions.IcebergSparkSessionExtensions")\
            .config("spark.sql.catalog.spark_catalog", "org.apache.iceberg.spark.SparkSessionCatalog")\
            .config("spark.sql.catalog.spark_catalog.type", "hive")\
            .config("spark.sql.catalog.blinkit_iceberg", "org.apache.iceberg.spark.SparkCatalog")\
            .config("spark.sql.blinkit_iceberg.handle-timestamp-without-timezone", "true")\
            .config("spark.sql.catalog.blinkit_iceberg.uri", "thrift://{config_dict["hive_metastore_uris"]["blinkit_iceberg"]}:9083")\
            .config("spark.sql.catalog.blinkit_iceberg.type", "hive")\
            .config("spark.sql.catalog.blinkit_iceberg_staging", "org.apache.iceberg.spark.SparkCatalog")\
            .config("spark.sql.blinkit_iceberg_staging.handle-timestamp-without-timezon", "true")\
            .config("spark.sql.catalog.blinkit_iceberg_staging.type", "hive")\
            .config("spark.sql.catalog.blinkit_iceberg_staging.uri", "thrift://{config_dict["hive_metastore_uris"]["blinkit_iceberg"]}:9084")\
            .config("spark.serializer", "org.apache.spark.serializer.KryoSerializer")\
            .config("spark.pyspark.python", "python3")\
            .config("spark.shuffle.mapStatus.compression.codec", "lz4")\
            .config("spark.hadoop.fs.s3a.committer.name", "directory")\
            .config("spark.hadoop.mapreduce.fileoutputcommitter.algorithm.version", "2")\
            .config("spark.hadoop.mapreduce.fileoutputcommitter.cleanup-failures.ignored", "true")\
            .config("spark.dynamicAllocation.executorIdleTimeout","900s")\
            .config("spark.dynamicAllocation.minExecutors","1")\
            .config("spark.dynamicAllocation.maxExecutors","6")\
            .config("spark.executor.memory","10g")\
            .config("spark.jars","{spark_jars}")\
            .config("spark.log.level","ERROR")\
            .config("spark.plugins","io.dataflint.spark.SparkDataflintPlugin")\
            .config("spark.sql.autoBroadcastJoinThreshold","-1")\
        """
        
    # add zspark as pyFiles
    add_py_files_command = """
        spark.sparkContext.addPyFile("s3://blinkit-analytics/config/pyjumbo/staging/config/spark_tools_config.py")
    """

    for key,value in session_configurations.get("conf",{}).items():
        extra_configurations += f'.config("{key}","{value}")'

    
    spark_submit_command = spark_submit_command + extra_configurations + ".getOrCreate()"
    spark_submit_command.replace("\\","").replace("\n","").replace("\t","")
    
    spark_submit_command = spark_submit_command + add_py_files_command

    shell.run_cell("print('Creating Spark Session')",store_history=False,silent=True)
    response = shell.run_cell(str(spark_submit_command),store_history=False,silent=False)
    if response.success:
        shell.db['SPARK_SESSION_COUNT'] = spark_session_count + 1
    return response.success


def stop_spark_eks_session(shell):
    if shell.db.get("SPARK_SESSION_COUNT",0) > 0:
        shell.db["SPARK_SESSION_COUNT"] = shell.db.get("SPARK_SESSION_COUNT", 0) - 1
    shell.run_cell("try: spark.stop()\nexcept: print('Spark session not found')", store_history=False, silent=True)


