# Copyright (c) 2015  <EMAIL>
# Distributed under the terms of the Modified BSD License.
from sparkmagic.utils.constants import LANG_PYTHON
from sparkmagic.kernels.wrapperkernel.sparkkernelbase import SparkKernelBase
from sparkmagic.livyclientlib.exceptions import wrap_unexpected_exceptions

from sparkmagic.kernels.kernelmagics import KernelMagics
import sparkmagic.utils.configuration as conf
from sparkeks_kernel.spark_eks import start_spark_eks_session, stop_spark_eks_session



class UserCodeParser(object):
    # A list of the names of all magics that are cell magics, but which have no cell body input.
    # For example, the %%info magic has no cell body input, i.e. it is incorrect to call
    #    %%info
    #    some_input
    _magics_with_no_cell_body = [
        i.__name__
        for i in [
            KernelMagics.info,
            KernelMagics.logs,
            KernelMagics.cleanup,
            KernelMagics.delete,
            KernelMagics.help,
            KernelMagics.spark,
            KernelMagics.send_to_spark
        ]
    ]

    def get_code_to_run(self, code):
        try:
            all_but_first_line = code.split(None, 1)[1]
        except IndexError:
            all_but_first_line = ""

        if code.startswith("%%local") or code.startswith("%local"):
            return all_but_first_line
        elif any(code.startswith("%%" + s) for s in self._magics_with_no_cell_body):
            return all_but_first_line
        elif any(code.startswith("%" + s) for s in self._magics_with_no_cell_body):
            return all_but_first_line
        elif code.startswith("%%") or code.startswith("%"):
            # If they use other line magics:
            #       %autosave
            #       my spark code
            # my spark code would be run locally and there might be an error.
            return code
        else:
            return code

class SparkEksKernel(SparkKernelBase):
    def __init__(self, **kwargs):
        implementation = "SparkEks"
        implementation_version = "1.0"
        language = LANG_PYTHON
        language_version = "0.1"
        language_info = {
            "name": "pyspark",
            "mimetype": "text/x-python",
            "codemirror_mode": {"name": "python", "version": 3},
            "file_extension": ".py",
            "pygments_lexer": "python3",
        }

        session_language = LANG_PYTHON
        user_code_parser = UserCodeParser()

        super().__init__(
            implementation,
            implementation_version,
            language,
            language_version,
            language_info,
            session_language,
            user_code_parser,
            **kwargs
        )

    def _load_magics_extension(self):
        register_magics_code = "%load_ext sparkeks_kernel.kernel_magics"
        self._execute_cell(
            register_magics_code,
            True,
            False,
            shutdown_if_error=True,
            log_if_error="Failed to load the Spark kernels magics library.",
        )
        self.logger.debug("Loaded magics.")
        super(SparkEksKernel, self)._load_magics_extension()
    

    def do_execute(
        self, code, silent, store_history=True, user_expressions=None, allow_stdin=False
    ):
        if not (code.startswith("%%") or code.startswith("%")): 
            self.start_spark_session()
        def f(self):
            if self._fatal_error is not None:
                return self._repeat_fatal_error()

            return self._do_execute(
                code, silent, store_history, user_expressions, allow_stdin
            )

        # Execute the code and handle exceptions
        wrapped = wrap_unexpected_exceptions(f, self._complete_cell)
        return wrapped(self)



    def start_spark_session(self):
        spark_session_started = conf.get_session_properties(self.language).get("spark_session_started", False)
        if spark_session_started:
            return
        session_configurations = conf.get_session_properties(self.language)
        response = start_spark_eks_session(self.shell,self.ipython_display, session_configurations)
        if response:
            self.ipython_display.write("Spark session started\n ")
            conf.override(conf.session_configs.__name__, {"spark_session_started":True})
        
            
    

    def do_shutdown(self, restart):
        # Cleanup
        stop_spark_eks_session(self.shell)
        self._delete_session()
        return self._do_shutdown_ipykernel(restart)

if __name__ == "__main__":
    from ipykernel.kernelapp import IPKernelApp

    IPKernelApp.launch_instance(kernel_class=SparkEksKernel)
