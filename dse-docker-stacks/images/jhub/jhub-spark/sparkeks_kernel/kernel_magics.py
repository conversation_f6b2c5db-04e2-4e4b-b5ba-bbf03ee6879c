from __future__ import print_function
from IPython.core.magic import (Magics, magics_class, cell_magic)

import json
from IPython.core.magic_arguments import  magic_arguments, argument
from sparkmagic.utils.constants import LANG_PYTHON

import sparkmagic.utils.configuration as conf
from sparkmagic.utils.utils import (
    parse_argstring_or_throw,
)
from sparkmagic.utils.sparkevents import SparkEvents
from sparkmagic.livyclientlib.exceptions import (
    handle_expected_exceptions,
    wrap_unexpected_exceptions
)
from hdijupyterutils.ipythondisplay import IpythonDisplay
from collections import namedtuple
from sparkmagic.utils.sparklogger import SparkLog
from sparkeks_kernel.spark_eks import start_spark_eks_session, stop_spark_eks_session
from sparkmagic.livyclientlib.exceptions import BadUserConfigurationException

# How to display different cell content types in IPython
SparkOutputHandler = namedtuple("SparkOutputHandler", ["html", "text", "default"])
def looks_like_json(s):
    return s.startswith("{") and s.endswith("}")

@magics_class
class SparkEksMagics(Magics):

    def __init__(self, shell, spark_session_started=0, spark_events=None):
        # You must call the parent constructor
        super(SparkEksMagics, self).__init__(shell)
        self.spark_session_started = spark_session_started
        self.logger = SparkLog("SparkMagics")
        self.ipython_display = IpythonDisplay()

        self.logger.debug("Initialized spark magics.")

        if spark_events is None:
            spark_events = SparkEvents()
        spark_events.emit_library_loaded_event()
        conf.override(conf.session_configs.__name__, {"spark_session_started":spark_session_started}) 


    @magic_arguments()
    @cell_magic
    @argument(
        "-f",
        "--force",
        type=bool,
        default=False,
        nargs="?",
        const=True,
        help="If present, user understands.",
    )
    @wrap_unexpected_exceptions
    @handle_expected_exceptions
    def configure_eks(self, line, cell="", local_ns=None):
        try:
            dictionary = json.loads(cell)
        except ValueError:
            self.ipython_display.send_error(
                "Could not parse JSON object from input '{}'".format(cell)
            )
            return
        error_msg, valid_config = self.validate_session_settings(dictionary)
        if not valid_config:
            raise BadUserConfigurationException(error_msg)
        args = parse_argstring_or_throw(self.configure_eks, line)
        if conf.get_session_properties(LANG_PYTHON).get("spark_session_started", False):
            if not args.force:
                self.ipython_display.send_error(
                    "A session has already been started. If you intend to recreate the "
                    "session with new configurations, please include the -f argument."
                )
                return
            else:
                stop_spark_eks_session(self.shell)
                self._override_session_settings(dictionary)
                response =  start_spark_eks_session(self.shell,self.ipython_display, conf.get_session_properties(LANG_PYTHON))
                if response:
                    conf.override(conf.session_configs.__name__, {"spark_session_started":True})
                else:
                    self.ipython_display.send_error("Failed to start spark session")
        else:
            self._override_session_settings(dictionary)

    @staticmethod
    def _override_session_settings(settings):
        conf.override(conf.session_configs.__name__, settings)
    
    def validate_session_settings(self, dictionary) :
        max_executors = int(dictionary.get("conf", {}).get("spark.dynamicAllocation.maxExecutors", "0"))

        if max_executors and max_executors > 30:
            return "Wrong config, cant set spark max executors more than 30", False
        executor_cores = dictionary.get("spark.executor.cores", 0)
        if executor_cores and executor_cores > 3:
            return "Wrong config, cant set spark executor cores more than 3", False
        max_memory = dictionary.get("conf", {}).get("spark.executor.memory","1g")
        if int(max_memory[:-1]) > 45 and max_memory[-1] == 'g':
            return "Wrong config, cant set spark executor memory more than 45g", False
        spark_jars = dictionary.get("conf", {}).get("spark.jars", "")
        if spark_jars:
            return "spark.jars is being set internally, use spark.jars.packages", False
        return "", True


def load_ipython_extension(ipython):
    """
    Any module file that define a function named `load_ipython_extension`
    can be loaded via `%load_ext module.path` or be configured to be
    autoloaded by IPython at startup time.
    """
    # This class must then be registered with a manually created instance,
    # since its constructor has different arguments from the default:
    spark_session_started = False
    magics = SparkEksMagics(ipython, spark_session_started)
    ipython.register_magics(magics)
