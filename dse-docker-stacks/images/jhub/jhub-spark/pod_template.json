{"apiVersion": "v1", "kind": "Pod", "spec": {"initContainers": [{"name": "python-package-installer", "image": "125719378300.dkr.ecr.ap-southeast-1.amazonaws.com/spark-platform:0.0.2_5d8d18fbf144", "command": ["bash", "-c", "pip install getname -t /python-packages/ && cp -r /usr/local/lib/python3.10/dist-packages/* /python-packages/"], "volumeMounts": [{"name": "package-volume", "mountPath": "/python-packages"}]}], "containers": [{"name": "spark-kubernetes-executor", "volumeMounts": [{"name": "package-volume", "mountPath": "/usr/local/lib/python3.10/dist-packages"}]}], "volumes": [{"name": "package-volume", "emptyDir": {}}], "tolerations": [{"effect": "NoSchedule", "key": "node-type", "operator": "Equal", "value": "prod-blinkit-spark-jhub-executor-spot"}], "nodeSelector": {"tenant": "blinkit", "node-type": "prod-blinkit-spark-jhub-executor-spot"}}}