
import os
import shutil

def get_vscode_executable():
    """Find the path to the code-server executable"""
    return shutil.which('code-server')

c.ServerProxy.servers = {
    'vscode': {
        'command': [get_vscode_executable(), '--auth', 'none', '--disable-telemetry', '--port', '{port}'],
        'timeout': 20,
        'launcher_entry': {
            'enabled': True,
            'title': 'VS Code'
        }
    }
}
