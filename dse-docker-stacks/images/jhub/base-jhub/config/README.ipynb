{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["To access our private GitHub repos seamlessly, you need to set up an SSH key and a GPG key, and add them to your GitHub account. This notebook will help you do that, so run each cell one by one and follow along!"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generate a new SSH key\n", "\n", "(For accessing private GitHub repos)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Please update the variables below."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["fullname = \"Your full name\"\n", "email = \"Email associated with your GitHub account\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!mkdir -p $HOME/.ssh"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not os.path.exists(\"/home/<USER>/.ssh/id_rsa.pub\"):\n", "    !ssh-keygen -t rsa -b 4096 -C \"jhub-prod.grofer.io\" -f \"$HOME/.ssh/id_rsa\" -q -N \"\"\n", "else:\n", "    print(\"ssh key exists!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If the output of the cell above was \"ssh key exists!\", then you may have already set up your SSH key on GitHub. If the output was something else, please copy the output of the cell below (your SSH public key) and [add it to GitHub](https://help.github.com/en/articles/adding-a-new-ssh-key-to-your-github-account)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!cat \"$HOME/.ssh/id_rsa.pub\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generate a new GPG key\n", "\n", "(For making verified commits on GitHub repos)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["keygen_params = f\"\"\"\n", "%no-protection\n", "Key-Type: 1\n", "Key-Length: 4096\n", "Subkey-Type: 1\n", "Subkey-Length: 4096\n", "Name-Real: {fullname}\n", "Name-Email: {email}\n", "Expire-Date: 0\n", "\"\"\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["if not os.path.exists(\"/home/<USER>/.gnupg\"):\n", "    !gpg --batch --gen-key <<EOF$keygen_params\n", "else:\n", "    print(\"gpg key exists!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If the output of the cell above was \"gpg key exists!\", then you may have already set up your GPG key on GitHub."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!gpg --list-secret-keys --keyid-format LONG | grep -w \"sec   rsa4096\" | cut -d' ' -f4 | cut -d'/' -f2"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gpg_key_id = !gpg --list-secret-keys --keyid-format LONG | grep -w \"sec   rsa4096\" | cut -d' ' -f4 | cut -d'/' -f2\n", "gpg_key_id = gpg_key_id[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If the output for the cell above the last two cells was something else, please copy the output of the cell below (your GPG public key) and [add it to GitHub](https://help.github.com/en/github/authenticating-to-github/adding-a-new-gpg-key-to-your-github-account#adding-a-gpg-key)."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["!gpg --armor --export $gpg_key_id"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generating a .gitconfig\n", "\n", "(So that git makes commits with your name, email and gpg key)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["gitconfig = f\"\"\"\n", "[user]\n", "        name = {fullname}\n", "        email = {email}\n", "        signingkey = {gpg_key_id}\n", "[commit]\n", "        gpgsign = true\n", "[gpg]\n", "        program = gpg\n", "[credential]\n", "        helper = cache\n", "\"\"\"\n", "\n", "with open(\"{home}/.gitconfig\".format(home=os.environ[\"HOME\"]), \"w\") as f:\n", "    f.write(gitconfig)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generating a conda environment\n", "\n", "By default, the packages you install won't persist across server restarts. To have them persist, you need to create a conda environment and install the packages you need in there. Furthermore, to use the packages from these environments, you need to install `ipykernel` alongside your packages. Here's an example:\n", "\n", "<pre>\n", "$ conda create -n myenv ipykernel scipy numpy &lt;package1&gt; &lt;package2&gt; ... &lt;packageN&gt;\n", "</pre>"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.6.9"}}, "nbformat": 4, "nbformat_minor": 4}