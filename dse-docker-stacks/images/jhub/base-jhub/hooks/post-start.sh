cp /root/.bashrc /home/<USER>/.bashrc
cp /root/.condarc /home/<USER>/.condarc
cp /root/.profile /home/<USER>/.profile
cp /root/README.ipynb /home/<USER>/README.ipynb

CONFIG=/tmp/config.json.tpl
if test -f "$CONFIG"; then
    mkdir -p /home/<USER>/.sparkmagic
fi

envsubst < /tmp/config.json.tpl | cat > /home/<USER>/.sparkmagic/config.json

conda init zsh

mkdir -p /home/<USER>/Documents
mkdir -p /home/<USER>/.conda-envs
mkdir -p /home/<USER>/Development/grofers

if [ -f /home/<USER>/.ssh/id_rsa ] && [ -f /home/<USER>/.gitconfig ]; then
    gitpuller **************:grofers/dse-cookbooks master /home/<USER>/Cookbooks
fi

if [ ! -L /home/<USER>/Shared ]; then
    ln -s /shared/Shared /home/<USER>/Shared
fi

python -m ipykernel install --user --name python3 --display-name "Python 3 (ipykernel)"

if [ "$JUPYTER_DOWNLOAD_ENABLED" = "false" ]; then
    echo "Disabling download functionality for user..."
    jupyter labextension disable @jupyterlab/docmanager-extension:download
    jupyter labextension disable @jupyterlab/filebrowser-extension:download
else
    echo "Download functionality is enabled"
fi