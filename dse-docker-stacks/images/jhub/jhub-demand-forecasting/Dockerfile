# Taking Base JHub Image as base Image, which include jupyter/datascience-notebook:lab-1.2.5 + some additional dependencies
ARG BASE_JHUB
FROM $BASE_JHUB

# Image Type/ Image Tag can be nightly(Latest Master build), or Latest(latest generated release)
ARG IMAGE_TYPE

# Taking Github Token to access dependencies from github
ARG GITHUB_TOKEN

#Switching user to root as dependencies needed to be installed for root user
USER root

#Installing pencilbox Requirements
COPY requirements.$IMAGE_TYPE/requirements.txt /tmp/requirements.txt
RUN pip install --index-url https://pypi.grofer.io/simple --no-cache-dir -r /tmp/requirements.txt

#Switching user
USER $NB_UID

RUN pip install jupyterlab==3.3.2 jupyterlab-server==2.10.3 && \
    pip install jupyter-server-proxy==3.2.1 && \
    pip install --user tensorflow==2.16.1 && \
    pip install --user jupyter-tensorboard==0.2.0
