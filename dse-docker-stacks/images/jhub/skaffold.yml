apiVersion: skaffold/v2beta26
kind: Config
.hooks: &default-hooks
  before:
  - command: ["/bin/bash", "-c", "if [ $runHooks = true ]; then MANIFEST=$(aws ecr batch-get-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-ids imageTag=$SKAFFOLD_IMAGE_TAG --output json | jq --raw-output --join-output '.images[0].imageManifest'); echo $MANIFEST > latest_backup.json; aws ecr put-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-tag $SKAFFOLD_IMAGE_TAG-$(date +%Y%m%d%H%M) --image-manifest file://latest_backup.json; fi"]
  after:
  - command: ["/bin/bash", "-c", "if [[ $tagToStable = true && $runHooks = true ]]; then MANIFEST=$(aws ecr batch-get-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-ids imageTag=stable --output json | jq --raw-output --join-output '.images[0].imageManifest'); echo $MANIFEST > stable_backup.json; aws ecr put-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-tag stable-$(date +%Y%m%d%H%M) --image-manifest file://stable_backup.json; fi"]
  - command: ["/bin/bash", "-c", "if [[ $tagToStable = true || $runHooks = true ]]; then MANIFEST=$(aws ecr batch-get-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-ids imageTag=$SKAFFOLD_IMAGE_TAG --output json | jq --raw-output --join-output '.images[0].imageManifest'); echo $MANIFEST > latest_stable.json; aws ecr put-image --repository-name $(cut -d':' -f1 <<<${SKAFFOLD_IMAGE:50}) --image-tag stable --image-manifest file://latest_stable.json; fi"]
profiles:
- name: nightly
  build:
    tagPolicy:
      envTemplate:
        template: nightly
    artifacts:
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/base-jhub
      context: ./base-jhub
      docker:
        buildArgs:
          BASE_JHUB_IMAGE: public.ecr.aws/zomato/jupyter/datascience-notebook:lab-3.2.8
          IMAGE_TYPE: nightly
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
        noCache: false
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/jhub/pencilbox
      context: ./jhub-pencilbox
      docker:
        buildArgs:
          IMAGE_TYPE: nightly
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
        noCache: true
      requires:
      - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/base-jhub
        alias: BASE_JHUB
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/jhub/orbis
      context: ./jhub-orbis
      docker:
        buildArgs:
          IMAGE_TYPE: nightly
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
        noCache: true
      requires:
      - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/base-jhub
        alias: BASE_JHUB
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/jhub/demand_forecasting
      context: ./jhub-demand-forecasting
      docker:
        buildArgs:
          IMAGE_TYPE: nightly
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
        noCache: true
      requires:
      - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/base-jhub
        alias: BASE_JHUB
    - image: registry.grofer.io/data/jhub/spark
      context: ./jhub-spark
      hooks: *default-hooks
      docker:
        buildArgs:
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
        noCache: true
      requires:
      - image: registry.grofer.io/data/base-jhub
        alias: BASE_JHUB
    local:
      push: true
- name: latest
  build:
    tagPolicy:
      envTemplate:
        template: latest
    artifacts:
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/base-jhub
      context: ./base-jhub
      hooks: *default-hooks
      docker:
        buildArgs:
          BASE_JHUB_IMAGE: public.ecr.aws/zomato/jupyter/datascience-notebook:lab-3.2.8
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
        noCache: false
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/jhub/pencilbox
      context: ./jhub-pencilbox
      hooks: *default-hooks
      docker:
        buildArgs:
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
        noCache: true
      requires:
      - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/base-jhub
        alias: BASE_JHUB
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/jhub/orbis
      context: ./jhub-orbis
      hooks: *default-hooks
      docker:
        buildArgs:
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
        noCache: true
      requires:
      - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/base-jhub
        alias: BASE_JHUB
    - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/jhub/demand_forecasting
      context: ./jhub-demand-forecasting
      hooks: *default-hooks
      docker:
        buildArgs:
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
        noCache: true
      requires:
      - image: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/base-jhub
        alias: BASE_JHUB
    - image: registry.grofer.io/data/jhub/spark
      context: ./jhub-spark
      hooks: *default-hooks
      docker:
        buildArgs:
          IMAGE_TYPE: latest
          GITHUB_TOKEN: "{{ .VAULT_AUTH_GITHUB_TOKEN }}"
        noCache: true
      requires:
      - image: registry.grofer.io/data/base-jhub
        alias: BASE_JHUB
    local:
      push: true
