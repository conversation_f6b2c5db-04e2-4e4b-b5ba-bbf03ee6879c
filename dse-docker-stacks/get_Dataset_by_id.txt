[{"type": "text", "text": "{\n  \"description_columns\": {},\n  \"id\": 4160,\n  \"label_columns\": {\n    \"always_filter_main_dttm\": \"Always Filter Main Dttm\",\n    \"cache_timeout\": \"Cache Timeout\",\n    \"changed_by.first_name\": \"Changed By First Name\",\n    \"changed_by.last_name\": \"Changed By Last Name\",\n    \"changed_on\": \"Changed On\",\n    \"changed_on_humanized\": \"Changed On Humanized\",\n    \"column_formats\": \"Column Formats\",\n    \"columns.advanced_data_type\": \"Columns Advanced Data Type\",\n    \"columns.changed_on\": \"Columns Changed On\",\n    \"columns.column_name\": \"Columns Column Name\",\n    \"columns.created_on\": \"Columns Created On\",\n    \"columns.description\": \"Columns Description\",\n    \"columns.expression\": \"Columns Expression\",\n    \"columns.extra\": \"Columns Extra\",\n    \"columns.filterable\": \"Columns Filterable\",\n    \"columns.groupby\": \"Columns Groupby\",\n    \"columns.id\": \"Columns Id\",\n    \"columns.is_active\": \"Columns Is Active\",\n    \"columns.is_dttm\": \"Columns Is Dttm\",\n    \"columns.python_date_format\": \"Columns Python Date Format\",\n    \"columns.type\": \"Columns Type\",\n    \"columns.type_generic\": \"Columns Type Generic\",\n    \"columns.uuid\": \"Columns Uuid\",\n    \"columns.verbose_name\": \"Columns Verbose Name\",\n    \"created_by.first_name\": \"Created By First Name\",\n    \"created_by.last_name\": \"Created By Last Name\",\n    \"created_on\": \"Created On\",\n    \"created_on_humanized\": \"Created On Humanized\",\n    \"currency_formats\": \"Currency Formats\",\n    \"database.backend\": \"Database Backend\",\n    \"database.database_name\": \"Database Database Name\",\n    \"database.id\": \"Database Id\",\n    \"datasource_name\": \"Datasource Name\",\n    \"datasource_type\": \"Datasource Type\",\n    \"default_endpoint\": \"Default Endpoint\",\n    \"description\": \"Description\",\n    \"extra\": \"Extra\",\n    \"fetch_values_predicate\": \"Fetch Values Predicate\",\n    \"filter_select_enabled\": \"Filter Select Enabled\",\n    \"granularity_sqla\": \"Granularity Sqla\",\n    \"id\": \"Id\",\n    \"is_managed_externally\": \"Is Managed Externally\",\n    \"is_sqllab_view\": \"Is Sqllab View\",\n    \"kind\": \"Kind\",\n    \"main_dttm_col\": \"Main Dttm Col\",\n    \"metrics.changed_on\": \"Metrics Changed On\",\n    \"metrics.created_on\": \"Metrics Created On\",\n    \"metrics.currency\": \"Metrics Currency\",\n    \"metrics.d3format\": \"Metrics D3Format\",\n    \"metrics.description\": \"Metrics Description\",\n    \"metrics.expression\": \"Metrics Expression\",\n    \"metrics.extra\": \"Metrics Extra\",\n    \"metrics.id\": \"Metrics Id\",\n    \"metrics.metric_name\": \"Metrics Metric Name\",\n    \"metrics.metric_type\": \"Metrics Metric Type\",\n    \"metrics.verbose_name\": \"Metrics Verbose Name\",\n    \"metrics.warning_text\": \"Metrics Warning Text\",\n    \"name\": \"Name\",\n    \"normalize_columns\": \"Normalize Columns\",\n    \"offset\": \"Offset\",\n    \"order_by_choices\": \"Order By Choices\",\n    \"owners.first_name\": \"Owners First Name\",\n    \"owners.id\": \"Owners Id\",\n    \"owners.last_name\": \"Owners Last Name\",\n    \"schema\": \"Schema\",\n    \"select_star\": \"Select Star\",\n    \"sql\": \"Sql\",\n    \"table_name\": \"Table Name\",\n    \"template_params\": \"Template Params\",\n    \"time_grain_sqla\": \"Time Grain Sqla\",\n    \"uid\": \"Uid\",\n    \"url\": \"Url\",\n    \"verbose_map\": \"Verbose Map\"\n  },\n  \"result\": {\n    \"always_filter_main_dttm\": false,\n    \"cache_timeout\": -1,\n    \"changed_by\": {\n      \"first_name\": \"Himanchal\",\n      \"last_name\": \"Chandra\"\n    },\n    \"changed_on\": \"2025-09-09T15:59:06.355556\",\n    \"changed_on_humanized\": \"6 hours ago\",\n    \"column_formats\": {},\n    \"columns\": [\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2025-09-09T15:59:06.283719\",\n        \"column_name\": \"del_time\",\n        \"created_on\": \"2025-09-09T15:59:06.283714\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 47361,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DOUBLE\",\n        \"type_generic\": 0,\n        \"uuid\": \"26ec8077-91f4-4ba8-92ca-515c2a4afc07\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2025-09-09T15:59:06.283752\",\n        \"column_name\": \"del_15_mins\",\n        \"created_on\": \"2025-09-09T15:59:06.283751\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 47362,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DECIMAL(24, 4)\",\n        \"type_generic\": 0,\n        \"uuid\": \"f314fde8-0f90-49ed-b492-d19ac9f5bd84\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2025-09-09T15:59:06.283771\",\n        \"column_name\": \"dh_perc\",\n        \"created_on\": \"2025-09-09T15:59:06.283769\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 47363,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DECIMAL(15, 4)\",\n        \"type_generic\": 0,\n        \"uuid\": \"bbbe3f88-a809-4ed4-a5f6-8133bd44df16\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2025-09-09T15:59:06.283789\",\n        \"column_name\": \"milk_cart_pen\",\n        \"created_on\": \"2025-09-09T15:59:06.283788\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 47364,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DECIMAL(15, 4)\",\n        \"type_generic\": 0,\n        \"uuid\": \"c34ad3d4-8fe7-4b87-b36f-bae2926c02f2\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2025-09-09T15:59:06.283807\",\n        \"column_name\": \"fnv_cart_pen\",\n        \"created_on\": \"2025-09-09T15:59:06.283806\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 47365,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DECIMAL(15, 4)\",\n        \"type_generic\": 0,\n        \"uuid\": \"ca5b2033-b091-4a9e-a404-fc1a4025ec6c\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2025-09-09T15:59:06.283825\",\n        \"column_name\": \"year_qtr_order\",\n        \"created_on\": \"2025-09-09T15:59:06.283824\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 47366,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"INTEGER\",\n        \"type_generic\": 0,\n        \"uuid\": \"d61d9056-3be0-4fb6-8e4d-471a6a248113\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2025-09-09T15:59:06.283843\",\n        \"column_name\": \"type_order\",\n        \"created_on\": \"2025-09-09T15:59:06.283842\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 47367,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"INTEGER\",\n        \"type_generic\": 0,\n        \"uuid\": \"d48d9867-7ada-4394-9535-3d07b68139ca\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-08T10:36:32.368070\",\n        \"column_name\": \"month\",\n        \"created_on\": \"2024-11-08T10:36:32.368069\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 17886,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"VARCHAR\",\n        \"type_generic\": 1,\n        \"uuid\": \"d9a882a2-3358-4fdb-a8b0-df54f77ebbf9\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-11T09:10:16.312524\",\n        \"column_name\": \"city_name\",\n        \"created_on\": \"2024-11-11T09:10:16.312519\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 18176,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"VARCHAR\",\n        \"type_generic\": 1,\n        \"uuid\": \"44667b37-df17-4ace-8d9c-7290d8cdf763\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-08T10:36:32.368035\",\n        \"column_name\": \"net_category\",\n        \"created_on\": \"2024-11-08T10:36:32.368030\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 17885,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"VARCHAR\",\n        \"type_generic\": 1,\n        \"uuid\": \"b29b0b45-b990-4770-8b57-7800459a10ee\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-08T10:36:32.368090\",\n        \"column_name\": \"dau\",\n        \"created_on\": \"2024-11-08T10:36:32.368088\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 17887,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DOUBLE\",\n        \"type_generic\": 0,\n        \"uuid\": \"3db665a3-a3ba-425e-b81d-4fcf2b9c244a\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-08T10:36:32.368113\",\n        \"column_name\": \"orders\",\n        \"created_on\": \"2024-11-08T10:36:32.368111\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 17888,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"BIGINT\",\n        \"type_generic\": 0,\n        \"uuid\": \"55a870f8-8fdb-469b-8a13-487758ff282a\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-08T10:36:32.368136\",\n        \"column_name\": \"active_stores\",\n        \"created_on\": \"2024-11-08T10:36:32.368134\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 17889,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"BIGINT\",\n        \"type_generic\": 0,\n        \"uuid\": \"7c115e89-0529-4858-824c-24efd4148faa\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-08T10:36:32.368155\",\n        \"column_name\": \"opd\",\n        \"created_on\": \"2024-11-08T10:36:32.368153\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 17890,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DOUBLE\",\n        \"type_generic\": 0,\n        \"uuid\": \"562457cb-4abc-4f90-abea-1f7e5ba60eaf\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-08T10:36:32.368174\",\n        \"column_name\": \"new_dau\",\n        \"created_on\": \"2024-11-08T10:36:32.368173\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 17891,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DOUBLE\",\n        \"type_generic\": 0,\n        \"uuid\": \"2fdbe2fa-5ef1-4bf1-bcb7-c3a4ef0373b3\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-08T10:36:32.368193\",\n        \"column_name\": \"lu_dau\",\n        \"created_on\": \"2024-11-08T10:36:32.368192\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 17892,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DOUBLE\",\n        \"type_generic\": 0,\n        \"uuid\": \"d53e8fc9-8902-4a7a-8bed-af50241b5eef\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-08T10:36:32.368213\",\n        \"column_name\": \"dau_mov\",\n        \"created_on\": \"2024-11-08T10:36:32.368211\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 17893,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DOUBLE\",\n        \"type_generic\": 0,\n        \"uuid\": \"486d0e93-26c8-4f33-82b8-993355bcdb9d\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-08T10:36:32.368250\",\n        \"column_name\": \"dau_atc\",\n        \"created_on\": \"2024-11-08T10:36:32.368249\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 17895,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DOUBLE\",\n        \"type_generic\": 0,\n        \"uuid\": \"5a3c213f-594d-4238-af73-e0fd823dc59b\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2025-05-20T06:19:11.360859\",\n        \"column_name\": \"cv_checkout\",\n        \"created_on\": \"2025-05-20T06:19:11.360853\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 36565,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DOUBLE\",\n        \"type_generic\": 0,\n        \"uuid\": \"875976cb-7dae-4619-a0df-8222bf18dc00\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2025-05-20T06:19:11.360922\",\n        \"column_name\": \"conv\",\n        \"created_on\": \"2025-05-20T06:19:11.360920\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 36566,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DOUBLE\",\n        \"type_generic\": 0,\n        \"uuid\": \"bac774dc-5a16-42fd-87ff-c42952b76eab\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-08T10:36:32.368289\",\n        \"column_name\": \"aov\",\n        \"created_on\": \"2024-11-08T10:36:32.368287\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 17897,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"DOUBLE\",\n        \"type_generic\": 0,\n        \"uuid\": \"d0a1f7bf-09e2-4054-9830-cdf5f0c7f8cb\",\n        \"verbose_name\": null\n      },\n      {\n        \"advanced_data_type\": null,\n        \"changed_on\": \"2024-11-11T12:15:30.327011\",\n        \"column_name\": \"cat_rank\",\n        \"created_on\": \"2024-11-11T12:15:30.327007\",\n        \"description\": null,\n        \"expression\": null,\n        \"extra\": \"{}\",\n        \"filterable\": true,\n        \"groupby\": true,\n        \"id\": 18214,\n        \"is_active\": true,\n        \"is_dttm\": false,\n        \"python_date_format\": null,\n        \"type\": \"BIGINT\",\n        \"type_generic\": 0,\n        \"uuid\": \"8f244fbf-131c-4381-b14f-be40a06af91f\",\n        \"verbose_name\": null\n      }\n    ],\n    \"created_by\": {\n      \"first_name\": \"Himanchal\",\n      \"last_name\": \"Chandra\"\n    },\n    \"created_on\": \"2024-11-08T10:36:32.031594\",\n    \"created_on_humanized\": \"10 months ago\",\n    \"currency_formats\": {},\n    \"database\": {\n      \"backend\": \"trino\",\n      \"database_name\": \" blinkit-growth-adhoc-cluster\",\n      \"id\": 15\n    },\n    \"datasource_name\": \"consumer_etls_growth_dashboard_1\",\n    \"datasource_type\": \"table\",\n    \"default_endpoint\": null,\n    \"description\": null,\n    \"extra\": null,\n    \"fetch_values_predicate\": null,\n    \"filter_select_enabled\": true,\n    \"granularity_sqla\": [],\n    \"id\": 4160,\n    \"is_managed_externally\": false,\n    \"is_sqllab_view\": false,\n    \"kind\": \"physical\",\n    \"main_dttm_col\": null,\n    \"metrics\": [\n      {\n        \"changed_on\": \"2024-11-08T10:36:32.388454\",\n        \"created_on\": \"2024-11-08T10:36:32.388451\",\n        \"currency\": null,\n        \"d3format\": null,\n        \"description\": null,\n        \"expression\": \"COUNT(*)\",\n        \"extra\": \"{\\\"warning_markdown\\\":\\\"\\\"}\",\n        \"id\": 4395,\n        \"metric_name\": \"count\",\n        \"metric_type\": \"count\",\n        \"verbose_name\": \"COUNT(*)\",\n        \"warning_text\": null\n      }\n    ],\n    \"name\": \"viz.consumer_etls_growth_dashboard_1\",\n    \"normalize_columns\": false,\n    \"offset\": 0,\n    \"order_by_choices\": [\n      [\n        \"[\\\"active_stores\\\", true]\",\n        \"active_stores [asc]\"\n      ],\n      [\n        \"[\\\"active_stores\\\", false]\",\n        \"active_stores [desc]\"\n      ],\n      [\n        \"[\\\"aov\\\", true]\",\n        \"aov [asc]\"\n      ],\n      [\n        \"[\\\"aov\\\", false]\",\n        \"aov [desc]\"\n      ],\n      [\n        \"[\\\"cat_rank\\\", true]\",\n        \"cat_rank [asc]\"\n      ],\n      [\n        \"[\\\"cat_rank\\\", false]\",\n        \"cat_rank [desc]\"\n      ],\n      [\n        \"[\\\"city_name\\\", true]\",\n        \"city_name [asc]\"\n      ],\n      [\n        \"[\\\"city_name\\\", false]\",\n        \"city_name [desc]\"\n      ],\n      [\n        \"[\\\"conv\\\", true]\",\n        \"conv [asc]\"\n      ],\n      [\n        \"[\\\"conv\\\", false]\",\n        \"conv [desc]\"\n      ],\n      [\n        \"[\\\"cv_checkout\\\", true]\",\n        \"cv_checkout [asc]\"\n      ],\n      [\n        \"[\\\"cv_checkout\\\", false]\",\n        \"cv_checkout [desc]\"\n      ],\n      [\n        \"[\\\"dau\\\", true]\",\n        \"dau [asc]\"\n      ],\n      [\n        \"[\\\"dau\\\", false]\",\n        \"dau [desc]\"\n      ],\n      [\n        \"[\\\"dau_atc\\\", true]\",\n        \"dau_atc [asc]\"\n      ],\n      [\n        \"[\\\"dau_atc\\\", false]\",\n        \"dau_atc [desc]\"\n      ],\n      [\n        \"[\\\"dau_mov\\\", true]\",\n        \"dau_mov [asc]\"\n      ],\n      [\n        \"[\\\"dau_mov\\\", false]\",\n        \"dau_mov [desc]\"\n      ],\n      [\n        \"[\\\"del_15_mins\\\", true]\",\n        \"del_15_mins [asc]\"\n      ],\n      [\n        \"[\\\"del_15_mins\\\", false]\",\n        \"del_15_mins [desc]\"\n      ],\n      [\n        \"[\\\"del_time\\\", true]\",\n        \"del_time [asc]\"\n      ],\n      [\n        \"[\\\"del_time\\\", false]\",\n        \"del_time [desc]\"\n      ],\n      [\n        \"[\\\"dh_perc\\\", true]\",\n        \"dh_perc [asc]\"\n      ],\n      [\n        \"[\\\"dh_perc\\\", false]\",\n        \"dh_perc [desc]\"\n      ],\n      [\n        \"[\\\"fnv_cart_pen\\\", true]\",\n        \"fnv_cart_pen [asc]\"\n      ],\n      [\n        \"[\\\"fnv_cart_pen\\\", false]\",\n        \"fnv_cart_pen [desc]\"\n      ],\n      [\n        \"[\\\"lu_dau\\\", true]\",\n        \"lu_dau [asc]\"\n      ],\n      [\n        \"[\\\"lu_dau\\\", false]\",\n        \"lu_dau [desc]\"\n      ],\n      [\n        \"[\\\"milk_cart_pen\\\", true]\",\n        \"milk_cart_pen [asc]\"\n      ],\n      [\n        \"[\\\"milk_cart_pen\\\", false]\",\n        \"milk_cart_pen [desc]\"\n      ],\n      [\n        \"[\\\"month\\\", true]\",\n        \"month [asc]\"\n      ],\n      [\n        \"[\\\"month\\\", false]\",\n        \"month [desc]\"\n      ],\n      [\n        \"[\\\"net_category\\\", true]\",\n        \"net_category [asc]\"\n      ],\n      [\n        \"[\\\"net_category\\\", false]\",\n        \"net_category [desc]\"\n      ],\n      [\n        \"[\\\"new_dau\\\", true]\",\n        \"new_dau [asc]\"\n      ],\n      [\n        \"[\\\"new_dau\\\", false]\",\n        \"new_dau [desc]\"\n      ],\n      [\n        \"[\\\"opd\\\", true]\",\n        \"opd [asc]\"\n      ],\n      [\n        \"[\\\"opd\\\", false]\",\n        \"opd [desc]\"\n      ],\n      [\n        \"[\\\"orders\\\", true]\",\n        \"orders [asc]\"\n      ],\n      [\n        \"[\\\"orders\\\", false]\",\n        \"orders [desc]\"\n      ],\n      [\n        \"[\\\"type_order\\\", true]\",\n        \"type_order [asc]\"\n      ],\n      [\n        \"[\\\"type_order\\\", false]\",\n        \"type_order [desc]\"\n      ],\n      [\n        \"[\\\"year_qtr_order\\\", true]\",\n        \"year_qtr_order [asc]\"\n      ],\n      [\n        \"[\\\"year_qtr_order\\\", false]\",\n        \"year_qtr_order [desc]\"\n      ]\n    ],\n    \"owners\": [\n      {\n        \"first_name\": \"Himanchal\",\n        \"id\": 5155,\n        \"last_name\": \"Chandra\"\n      }\n    ],\n    \"schema\": \"viz\",\n    \"select_star\": \"SELECT *\\nFROM viz.consumer_etls_growth_dashboard_1\\nLIMIT 100\",\n    \"sql\": \"\",\n    \"table_name\": \"consumer_etls_growth_dashboard_1\",\n    \"template_params\": null,\n    \"time_grain_sqla\": [\n      [\n        \"PT1S\",\n        \"Second\"\n      ],\n      [\n        \"PT1M\",\n        \"Minute\"\n      ],\n      [\n        \"PT1H\",\n        \"Hour\"\n      ],\n      [\n        \"P1D\",\n        \"Day\"\n      ],\n      [\n        \"P1W\",\n        \"Week\"\n      ],\n      [\n        \"P1M\",\n        \"Month\"\n      ],\n      [\n        \"P3M\",\n        \"Quarter\"\n      ],\n      [\n        \"P1Y\",\n        \"Year\"\n      ],\n      [\n        \"1969-12-28T00:00:00Z/P1W\",\n        \"Week starting Sunday\"\n      ],\n      [\n        \"1969-12-29T00:00:00Z/P1W\",\n        \"Week starting Monday\"\n      ],\n      [\n        \"P1W/1970-01-03T00:00:00Z\",\n        \"Week ending Saturday\"\n      ],\n      [\n        \"P1W/1970-01-04T00:00:00Z\",\n        \"Week ending Sunday\"\n      ]\n    ],\n    \"uid\": \"4160__table\",\n    \"url\": \"/tablemodelview/edit/4160\",\n    \"verbose_map\": {\n      \"__timestamp\": \"Time\",\n      \"active_stores\": \"active_stores\",\n      \"aov\": \"aov\",\n      \"cat_rank\": \"cat_rank\",\n      \"city_name\": \"city_name\",\n      \"conv\": \"conv\",\n      \"count\": \"COUNT(*)\",\n      \"cv_checkout\": \"cv_checkout\",\n      \"dau\": \"dau\",\n      \"dau_atc\": \"dau_atc\",\n      \"dau_mov\": \"dau_mov\",\n      \"del_15_mins\": \"del_15_mins\",\n      \"del_time\": \"del_time\",\n      \"dh_perc\": \"dh_perc\",\n      \"fnv_cart_pen\": \"fnv_cart_pen\",\n      \"lu_dau\": \"lu_dau\",\n      \"milk_cart_pen\": \"milk_cart_pen\",\n      \"month\": \"month\",\n      \"net_category\": \"net_category\",\n      \"new_dau\": \"new_dau\",\n      \"opd\": \"opd\",\n      \"orders\": \"orders\",\n      \"type_order\": \"type_order\",\n      \"year_qtr_order\": \"year_qtr_order\"\n    }\n  },\n  \"show_columns\": [\n    \"id\",\n    \"database.database_name\",\n    \"database.id\",\n    \"table_name\",\n    \"sql\",\n    \"filter_select_enabled\",\n    \"fetch_values_predicate\",\n    \"schema\",\n    \"description\",\n    \"main_dttm_col\",\n    \"normalize_columns\",\n    \"always_filter_main_dttm\",\n    \"offset\",\n    \"default_endpoint\",\n    \"cache_timeout\",\n    \"is_sqllab_view\",\n    \"template_params\",\n    \"select_star\",\n    \"owners.id\",\n    \"owners.first_name\",\n    \"owners.last_name\",\n    \"columns.advanced_data_type\",\n    \"columns.changed_on\",\n    \"columns.column_name\",\n    \"columns.created_on\",\n    \"columns.description\",\n    \"columns.expression\",\n    \"columns.filterable\",\n    \"columns.groupby\",\n    \"columns.id\",\n    \"columns.is_active\",\n    \"columns.extra\",\n    \"columns.is_dttm\",\n    \"columns.python_date_format\",\n    \"columns.type\",\n    \"columns.uuid\",\n    \"columns.verbose_name\",\n    \"metrics.changed_on\",\n    \"metrics.created_on\",\n    \"metrics.d3format\",\n    \"metrics.currency\",\n    \"metrics.description\",\n    \"metrics.expression\",\n    \"metrics.extra\",\n    \"metrics.id\",\n    \"metrics.metric_name\",\n    \"metrics.metric_type\",\n    \"metrics.verbose_name\",\n    \"metrics.warning_text\",\n    \"datasource_type\",\n    \"url\",\n    \"extra\",\n    \"kind\",\n    \"created_on\",\n    \"created_on_humanized\",\n    \"created_by.first_name\",\n    \"created_by.last_name\",\n    \"changed_on\",\n    \"changed_on_humanized\",\n    \"changed_by.first_name\",\n    \"changed_by.last_name\",\n    \"columns.type_generic\",\n    \"database.backend\",\n    \"columns.advanced_data_type\",\n    \"is_managed_externally\",\n    \"uid\",\n    \"datasource_name\",\n    \"name\",\n    \"column_formats\",\n    \"currency_formats\",\n    \"granularity_sqla\",\n    \"time_grain_sqla\",\n    \"order_by_choices\",\n    \"verbose_map\"\n  ],\n  \"show_title\": \"Show Sqla Table\"\n}", "uuid": "3ece4ad0-7410-4a0c-9f92-18581892febd"}]