name: airflow_s3_sync_build

on:
  workflow_dispatch:
    inputs:
      release-type:
        type: choice
        description: Release type
        options:
          - nightly
          - latest
      tagToStable:
        type: boolean
        description: Tag to Stable
      runHooks:
        type: boolean
        description: Backup current latest (useful incase of first time builds of new projects to avoid hook failures)
        default: true

env:
  REPO_NAME: data

jobs:
  build:
    runs-on: [ blinkit-ci-action-runner-default ]
    steps:
      - name: Check repository
        uses: actions/checkout@v3

      - name: Login to Prod Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1
        with:
          registries: "442534439095"
        env:
          AWS_REGION: 'ap-southeast-1'

      - name: Set up Docker Context for Buildx
        run: |
          docker context create builders || true
          docker context use builders
        shell: bash

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          endpoint: builders
          driver-opts: |
            image=public.ecr.aws/blinkit/moby/buildkit:v0.13.1

      - name: Build S3 sync
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/airflow-s3-sync
          DOCKER_BUILD_CONTEXT: images/airflow_s3_sync
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            IMAGE_TYPE=${{ github.event.inputs.release-type }}