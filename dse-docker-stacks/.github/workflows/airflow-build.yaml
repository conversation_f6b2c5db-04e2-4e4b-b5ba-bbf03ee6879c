name: airflow_build

on:
  workflow_dispatch:
    inputs:
      release-type:
        type: choice
        description: Release type
        options:
          - nightly
          - latest
      tagToStable:
        type: boolean
        description: Tag to Stable
      runHooks:
        type: boolean
        description: Backup current latest (useful incase of first time builds of new projects to avoid hook failures)
        default: true
      image-type:
        type: choice
        description: Which image types need to built
        options:
          - prep
          - de
          - common
          - dbt
          - ds
          - all-prod

env:
  REPO_NAME: data
  BASE_APACHE_AIRFLOW_IMAGE: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/apache-airflow-base:2.3.3-python-3.9

jobs:
  build:
    runs-on: [ blinkit-ci-action-runner-default ]
    steps:
      - name: Check repository
        uses: actions/checkout@v3

      - name: Login to Prod Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1
        with:
          registries: "442534439095"
        env:
          AWS_REGION: 'ap-southeast-1'

      - name: Set up Docker Context for Buildx
        run: |
          docker context create builders || true
          docker context use builders
        shell: bash

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          endpoint: builders
          driver-opts: |
            image=public.ecr.aws/blinkit/moby/buildkit:v0.13.1

      - name: Build prep
        if: ${{ github.event.inputs.image-type == 'prep' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/airflow-prep
          DOCKER_BUILD_CONTEXT: images/airflow/base-airflow
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_APACHE_IMAGE=${{ env.BASE_APACHE_AIRFLOW_IMAGE }}
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            FLAVOUR=prep
            IMAGE_TYPE=${{ github.event.inputs.release-type }}

      - name: Build prep/dbt
        if: ${{ github.event.inputs.image-type == 'prep' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/airflow-prep/dbt
          DOCKER_BUILD_CONTEXT: images/airflow/dbt-with-airflow
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_AIRFLOW=442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/${{ env.REPO_NAME }}/airflow-prep:${{ github.event.inputs.release-type }}
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            FLAVOUR=dbt
            IMAGE_TYPE=${{ github.event.inputs.release-type }}

      - name: Build common
        if: ${{ github.event.inputs.image-type == 'common' || github.event.inputs.image-type == 'ds' || github.event.inputs.image-type == 'all-prod' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/airflow-common
          DOCKER_BUILD_CONTEXT: images/airflow/base-airflow
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_APACHE_IMAGE=${{ env.BASE_APACHE_AIRFLOW_IMAGE }}
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            FLAVOUR=default
            IMAGE_TYPE=${{ github.event.inputs.release-type }}

      - name: Build de
        if: ${{ github.event.inputs.image-type == 'de' || github.event.inputs.image-type == 'dbt' || github.event.inputs.image-type == 'all-prod' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/airflow-de
          DOCKER_BUILD_CONTEXT: images/airflow/base-airflow
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_APACHE_IMAGE=${{ env.BASE_APACHE_AIRFLOW_IMAGE }}
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            FLAVOUR=de
            IMAGE_TYPE=${{ github.event.inputs.release-type }}

      - name: Build dbt
        if: ${{ github.event.inputs.image-type == 'dbt' || github.event.inputs.image-type == 'all-prod' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/airflow-de/dbt
          DOCKER_BUILD_CONTEXT: images/airflow/dbt-with-airflow
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_AIRFLOW=442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/${{ env.REPO_NAME }}/airflow-de:${{ github.event.inputs.release-type }}
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            FLAVOUR=dbt
            IMAGE_TYPE=${{ github.event.inputs.release-type }}

      - name: Build ds
        if: ${{ github.event.inputs.image-type == 'ds' || github.event.inputs.image-type == 'all-prod' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/airflow-ds-common
          DOCKER_BUILD_CONTEXT: images/airflow/ds-with-airflow
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_AIRFLOW=442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/${{ env.REPO_NAME }}/airflow-common:${{ github.event.inputs.release-type }}
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            FLAVOUR=default
            IMAGE_TYPE=${{ github.event.inputs.release-type }}
