name: dagger_build

on:
  workflow_dispatch:
    inputs:
      release-type:
        type: choice
        description: Release type
        options:
          - nightly
          - latest
      tagToStable:
        type: boolean
        description: Tag to Stable
      runHooks:
        type: boolean
        description: Backup current latest (useful incase of first time builds of new projects to avoid hook failures)
        default: true
      image-type:
        type: choice
        description: Which image types need to built
        options:
          - all
          - airflow-de
          - airflow

env:
  REPO_NAME: data
  BASE_IMAGE: public.ecr.aws/zomato/python:3.10

jobs:
  build:
    runs-on: [ blinkit-ci-action-runner-default ]
    steps:
      - name: Check repository
        uses: actions/checkout@v3

      - name: Login to Prod Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1
        with:
          registries: "442534439095"
        env:
          AWS_REGION: 'ap-southeast-1'

      - name: Set up Docker Context for Buildx
        run: |
          docker context create builders || true
          docker context use builders
        shell: bash

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          endpoint: builders
          driver-opts: |
            image=public.ecr.aws/blinkit/moby/buildkit:v0.13.1

      - name: Build dagger airflow-de-build
        if: ${{ github.event.inputs.image-type == 'airflow-de' || github.event.inputs.image-type == 'all' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/airflow-de-dags-build
          DOCKER_BUILD_CONTEXT: images/dagger/build
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_IMAGE=${{ env.BASE_IMAGE }}
            IMAGE_TYPE=${{ github.event.inputs.release-type }}
            FLAVOUR=airflow-de
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}

      - name: Build dagger airflow-build
        if: ${{ github.event.inputs.image-type == 'airflow' || github.event.inputs.image-type == 'all' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/airflow-dags-build
          DOCKER_BUILD_CONTEXT: images/dagger/build
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_IMAGE=${{ env.BASE_IMAGE }}
            IMAGE_TYPE=${{ github.event.inputs.release-type }}
            FLAVOUR=airflow
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
