name: jhub_build

on:
  workflow_dispatch:
    inputs:
      release-type:
        type: choice
        description: Release type
        options:
          - nightly
          - latest
      tagToStable:
        type: boolean
        description: Tag to Stable
      runHooks:
        type: boolean
        description: Backup current latest (useful incase of first time builds of new projects to avoid hook failures)
        default: true
      image-type:
        type: choice
        description: Which image types need to built
        options:
          - all
          - all-without-base
          - pencilbox
          - demand-forcasting
          - spark
          - base
          - base_python_3_10
          - gpu
          - experimental

env:
  REPO_NAME: data
  BASE_JHUB_IMAGE: public.ecr.aws/zomato/jupyter/datascience-notebook:lab-3.2.8
  BASE_JHUB_IMAGE_PYTHON_3_10: public.ecr.aws/zomato/jupyter/datascience-notebook:lab-4.0.0
  BASE_JHUB: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/base-jhub

jobs:
  build:
    runs-on: [ blinkit-ci-action-runner-default ]
    steps:
      - name: Check repository
        uses: actions/checkout@v3

      - name: Login to Prod Amazon ECR
        uses: aws-actions/amazon-ecr-login@v1
        with:
          registries: "442534439095"
        env:
          AWS_REGION: 'ap-southeast-1'

      - name: Set up Docker Context for Buildx
        run: |
          docker context create builders || true
          docker context use builders
        shell: bash

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          endpoint: builders
          driver-opts: |
            image=public.ecr.aws/blinkit/moby/buildkit:v0.13.1

      - name: Build base-jhub
        if: ${{ github.event.inputs.image-type == 'base' || github.event.inputs.image-type == 'all' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/base-jhub
          DOCKER_BUILD_CONTEXT: images/jhub/base-jhub
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_JHUB_IMAGE=${{ env.BASE_JHUB_IMAGE }}
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            IMAGE_TYPE=${{ github.event.inputs.release-type }}
      
      - name: Build base-jhub-python-3-10
        if: ${{ github.event.inputs.image-type == 'base_python_3_10'}}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/base-jhub
          DOCKER_BUILD_CONTEXT: images/jhub/base-jhub-python-3-10
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: false
          RELEASE_TYPE: python-3.10
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_JHUB_IMAGE=${{ env.BASE_JHUB_IMAGE_PYTHON_3_10 }}
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            IMAGE_TYPE=python-3.10

      - name: Build pencilbox
        if: ${{ github.event.inputs.image-type == 'pencilbox' || github.event.inputs.image-type == 'all' || github.event.inputs.image-type == 'all-without-base' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/jhub/pencilbox
          DOCKER_BUILD_CONTEXT: images/jhub/jhub-pencilbox
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_JHUB=${{ env.BASE_JHUB }}:${{ github.event.inputs.release-type }}
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            IMAGE_TYPE=${{ github.event.inputs.release-type }}

      - name: Build demand-forecasting
        if: ${{ github.event.inputs.image-type == 'demand-forcasting' || github.event.inputs.image-type == 'all' || github.event.inputs.image-type == 'all-without-base' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/jhub/demand_forecasting
          DOCKER_BUILD_CONTEXT: images/jhub/jhub-demand-forecasting
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_JHUB=${{ env.BASE_JHUB }}:${{ github.event.inputs.release-type }}
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            IMAGE_TYPE=${{ github.event.inputs.release-type }}

      - name: Build spark
        if: ${{ github.event.inputs.image-type == 'spark' || github.event.inputs.image-type == 'base_python_3_10' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/jhub/spark
          DOCKER_BUILD_CONTEXT: images/jhub/jhub-spark
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_JHUB=${{ env.BASE_JHUB }}:python-3.10
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            IMAGE_TYPE=${{ github.event.inputs.release-type }}

      - name: Build GPU
        if: ${{ github.event.inputs.image-type == 'gpu' || github.event.inputs.image-type == 'all' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/jhub/gpu
          DOCKER_BUILD_CONTEXT: images/jhub/base-jhub-gpu
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_JHUB=${{ env.BASE_JHUB }}:${{ github.event.inputs.release-type }}
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            IMAGE_TYPE=${{ github.event.inputs.release-type }}
      
      - name: Build Experimental
        if: ${{ github.event.inputs.image-type == 'experimental' || github.event.inputs.image-type == 'all' }}
        uses: ./.github/actions/builder
        with:
          ECR_REPO_NAME: ${{ env.REPO_NAME }}/jhub/pencilbox
          DOCKER_BUILD_CONTEXT: images/jhub/jhub-pencilbox
          GH_ACCESS_TOKEN: ${{ secrets.GH_ACCESS_TOKEN }}
          TAG_TO_STABLE: ${{ github.event.inputs.tagToStable }}
          RELEASE_TYPE: ${{ github.event.inputs.release-type }}
          RUN_HOOKS: ${{ github.event.inputs.runHooks }}
          DOCKER_BUILD_ARGS: |
            BASE_JHUB=${{ env.BASE_JHUB }}:${{ github.event.inputs.release-type }}
            GITHUB_TOKEN=${{ secrets.GH_ACCESS_TOKEN }}
            IMAGE_TYPE=${{ github.event.inputs.release-type }}

