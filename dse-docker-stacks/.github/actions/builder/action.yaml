name: 'Build Data Docker Images'
description: 'Build GH action for Data Docker Images'

inputs:
  AWS_ACCOUNT_PROD:
    description: 'Production AWS Account'
    required: false
    default: '************'
  AWS_ECR_REGION_PROD:
    description: 'Production ECR Region'
    required: false
    default: 'ap-southeast-1'
  AWS_S3_CACHE_BUCKET:
    description: 'AWS S3 Bucket for storing Cache'
    required: false
    default: 'blinkit-gh-action-cache'
  AWS_S3_CACHE_BUCKET_REGION:
    description: 'AWS S3 Bucket Region for storing Cache'
    required: false
    default: 'ap-southeast-1'
  ECR_REPO_NAME:
    description: 'ECR Repo Name'
    required: true
  DOCKER_BUILD_CONTEXT:
    description: 'Dockerfile context path'
    required: false
    default: '.'
  DOCKER_BUILD_FILE_NAME:
    description: 'Dockerfile Name'
    required: false
    default: 'Dockerfile'
  DOCKER_BUILD_ARGS:
    description: 'Build arguments for docker'
    required: false
    default: ' '
  TAG_TO_STABLE:
    description: Image will be retagged to stable (True/False)
    required: true
  RELEASE_TYPE:
    description: Release type to be build nightly, latest
    required: true
  RUN_HOOKS:
    description: Run backup scripts or not
    required: true
  GH_ACCESS_TOKEN:
    description: 'Github Secret token'
    required: false


runs:
  using: "composite"
  steps:

    - name: Backup latest image
      if: ${{ inputs.RUN_HOOKS && inputs.RELEASE_TYPE == 'latest' }}
      shell: bash
      run: |
        MANIFEST=$(aws ecr batch-get-image --repository-name $ECR_REPO_NAME --image-ids imageTag=latest --region $AWS_ECR_REGION_PROD --registry-id $AWS_ACCOUNT_PROD --output json | jq --raw-output --join-output '.images[0].imageManifest' 2>/dev/null)
        aws ecr put-image --repository-name $ECR_REPO_NAME --image-tag latest-$(date +'%Y%m%d%H%M') --region $AWS_ECR_REGION_PROD --registry-id $AWS_ACCOUNT_PROD --image-manifest "$MANIFEST" 2>/dev/null
      env:
        ECR_REPO_NAME: ${{ inputs.ECR_REPO_NAME }}
        AWS_ECR_REGION_PROD: ${{ inputs.AWS_ECR_REGION_PROD }}
        AWS_ACCOUNT_PROD: ${{ inputs.AWS_ACCOUNT_PROD }}

    - name: Docker metadata extraction
      id: meta
      uses: docker/metadata-action@v4
      with:
        flavor: |
          latest=false
        images: |
          ${{ inputs.AWS_ACCOUNT_PROD }}.dkr.ecr.${{ inputs.AWS_ECR_REGION_PROD }}.amazonaws.com/${{ inputs.ECR_REPO_NAME }}
        tags: |
          type=sha,format=long
          type=raw,value=${{ inputs.RELEASE_TYPE }}

    - name: Build and Push Docker Image
      uses: docker/build-push-action@v3
      with:
        context: ${{ inputs.DOCKER_BUILD_CONTEXT }}
        push: true
        file: ${{ inputs.DOCKER_BUILD_CONTEXT }}/${{ inputs.DOCKER_BUILD_FILE_NAME }}
        build-args: |
          ${{ inputs.DOCKER_BUILD_ARGS }}
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        secrets: |
          "github_token=${{ inputs.GH_ACCESS_TOKEN }}"

    - name: Backup stable image and tagging latest to stable
      if: ${{ inputs.TAG_TO_STABLE && inputs.RELEASE_TYPE == 'latest' }}
      shell: bash
      run: |
        MANIFEST=$(aws ecr batch-get-image --repository-name $ECR_REPO_NAME --image-ids imageTag=stable --region $AWS_ECR_REGION_PROD --registry-id $AWS_ACCOUNT_PROD --output json | jq --raw-output --join-output '.images[0].imageManifest' 2>/dev/null)
        aws ecr put-image --repository-name $ECR_REPO_NAME --image-tag stable-$(date +'%Y%m%d%H%M') --region $AWS_ECR_REGION_PROD --registry-id $AWS_ACCOUNT_PROD --image-manifest "$MANIFEST" 2>/dev/null
        MANIFEST=$(aws ecr batch-get-image --repository-name $ECR_REPO_NAME --image-ids imageTag=latest --region $AWS_ECR_REGION_PROD --registry-id $AWS_ACCOUNT_PROD --output json | jq --raw-output --join-output '.images[0].imageManifest' 2>/dev/null)
        aws ecr put-image --repository-name $ECR_REPO_NAME --image-tag stable --region $AWS_ECR_REGION_PROD --registry-id $AWS_ACCOUNT_PROD --image-manifest "$MANIFEST" 2>/dev/null
      env:
        ECR_REPO_NAME: ${{ inputs.ECR_REPO_NAME }}
        AWS_ECR_REGION_PROD: ${{ inputs.AWS_ECR_REGION_PROD }}
        AWS_ACCOUNT_PROD: ${{ inputs.AWS_ACCOUNT_PROD }}