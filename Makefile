ifndef ENV
$(error ENV is not set)
endif

RELEASE ?= jhub
K8S_NAMESPACE ?= jhub
DOCKER_REPOSITORY ?= 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/jhub

.PHONY: install
install:
	pip install -r requirements.txt
	scripts/jhub.py create_config $(ENV) $(DOCKER_REPOSITORY)

.PHONY: k8s-install
k8s-install:
	helm upgrade --install $(RELEASE) ./jupyterhub --namespace $(K8S_NAMESPACE) --values config-$(ENV).yml --timeout 3600s --wait

.PHONY: k8s-upgrade
k8s-upgrade:
	helm upgrade $(RELEASE) ./jupyterhub --namespace $(K8S_NAMESPACE) --values config-$(ENV).yml --timeout 3600s --wait


.PHONY: k8s-delete
k8s-delete:
	@if [ $(ENV) = "prod" ]; then\
                echo "Please do not delete";\
                exit 1;\
        fi
	helm delete $(K8S_NAMESPACE) --purge
