apiVersion: skaffold/v2beta23
kind: Config
profiles:
- name: persistence-volume
  deploy:
    kubeContext: {{ kubeContext }}
    kubectl:
      defaultNamespace: {{ namespace }}
      manifests:
      - {{ env }}/{{ namespace }}/pv.yaml
      - {{ env }}/{{ namespace }}/pvc.yaml
- name: service-account
  deploy:
    kubeContext: {{ kubeContext }}
    kubectl:
      defaultNamespace: {{ namespace }}
      manifests:
      - {{ env }}/{{ namespace }}/service-account.yaml
- name: jhub
  deploy:
    kubeContext: {{ kubeContext }}
    helm:
      releases:
      - name: {{ namespace }}
        repo: https://jupyterhub.github.io/helm-chart/
        remoteChart: jupyterhub
        version: 1.2.0
        namespace: {{ namespace }}
        valuesFiles:
          - {{ env }}/{{ namespace }}/common.yaml
          - {{ env }}/{{ namespace }}/values.yaml
        upgradeOnChange: true
