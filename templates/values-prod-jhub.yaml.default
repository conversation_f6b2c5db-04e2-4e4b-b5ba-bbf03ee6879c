hub:
    nodeSelector:
        nodetype: data-ondemand
        workload: data-ondemand-servers
    tolerations:
        - key: service
          operator: Equal
          value: data-ondemand-servers
          effect: NoSchedule
    config:
        GoogleOAuthenticator:
            oauth_callback_url: https://jhub-prod.grofer.io/hub/oauth_callback
    extraConfig:
        jupyter_config: |
            c.Spawner.cmd = ['jupyter-labhub']
            c.Spawner.args = ['--allow-root']
            c.DynamicKubeSpawner.image_pull_policy = 'Always'

            c.DynamicKubeSpawner.volumes = [{
                'name': 'efs',
                'persistentVolumeClaim': {
                    'claimName': 'jhub-efs-encrypted'
                }
            }]
            c.DynamicKubeSpawner.volume_mounts = [{
                'mountPath': '/home/<USER>',
                'name': 'efs',
                'subPath': 'home/{username}'
            }, {
                'mountPath': '/shared',
                'name': 'efs',
                'subPath': 'shared'
            }, {
                'mountPath': '/feeder-runs',
                'name': 'efs',
                'subPath': 'feeder-runs'
            }, {
                'mountPath': '/airflow-jhub-shared',
                'name': 'efs',
                'subPath': 'airflow-jhub-shared'
            }]
    extraEnv:
        VAULT_URL: https://vault-ui-prod.grofer.io
proxy:
    service:
        annotations:
            service.beta.kubernetes.io/aws-load-balancer-security-groups: sg-04625e7cfdf8f5e40
            service.beta.kubernetes.io/aws-load-balancer-ssl-cert: arn:aws:acm:ap-southeast-1:442534439095:certificate/99b32022-631f-4a1b-8e5c-71eec0b41d55
    chp:
        nodeSelector:
            nodetype: data-ondemand
            workload: data-ondemand-servers
        tolerations:
            - key: service
              operator: Equal
              value: data-ondemand-servers
              effect: NoSchedule
singleuser:
    profileList: []
    nodeSelector:
        nodetype: spot
    extraTolerations:
        - key: service
          operator: Equal
          value: jhub
          effect: NoSchedule
    extraEnv:
        VAULT_URL: https://vault-ui-prod.grofer.io