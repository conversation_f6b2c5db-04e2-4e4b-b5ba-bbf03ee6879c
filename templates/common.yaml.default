hub:
    image:
        name: 442534439095.dkr.ecr.ap-southeast-1.amazonaws.com/data/jhub/k8s-hub
        tag: "1.2.0"
    livenessProbe:
        enabled: true
        initialDelaySeconds: 30
        timeoutSeconds: 10
        periodSeconds: 60
    readinessProbe:
        enabled: true
        initialDelaySeconds: 30
        timeoutSeconds: 10
        periodSeconds: 60
    extraEnv:
        VAULT_TOKEN: "{{ github_token }}"
        VAULT_JHUB_SERVICE_TOKEN: "{{ vault_service_token }}"
        NETWORK_TOOLS_IMAGE_NAME: "jupyterhub/k8s-network-tools"
        NETWORK_TOOLS_IMAGE_TAG: "1.2.0"
        HUB_K8S_SA_TOKEN_PATH: "/var/run/secrets/kubernetes.io/serviceaccount/token"
        VAULT_K8S_AUTH_MOUNT: "eks"
        HUB_VAULT_K8S_AUTH_ROLE: "dse-jhub-jenkins-role"
    cookieSecret: "{{ cookie_secret }}"
    allowNamedServers: true
    namedServerLimitPerUser: 2
    db:
        type: postgres
        upgrade: true
        url: {{ db_uri }}
        password: {{ db_password }}
    config:
        Authenticator:
            admin_users:
                - <EMAIL>
                - <EMAIL>
                - <EMAIL>
                - <EMAIL>
                - <EMAIL>
        GoogleOAuthenticator:
            client_id: "{{ client_id }}"
            client_secret: "{{ client_secret }}"
            hosted_domain:
                - grofers.com
                - zomato.com
            login_service: Grofers
        JupyterHub:
            admin_access: true
            authenticator_class: google
    extraConfig:
        hub_config: |
            import sys
            import json
            from tornado import web
            from traitlets import List
            from kubespawner import KubeSpawner
            from slugify import slugify
            from jinja2 import Environment, BaseLoader
            from oauthenticator.google import GoogleOAuthenticator
            from collections import defaultdict
            import hvac
            import os
            import re
            from kubernetes import client
            import socket

            EMAIL_RE = re.compile(r'([a-zA-Z0-9\.]+)@([a-zA-Z0-9]+)\.com')

            def _authenticate_using_sa(client) -> hvac.Client:
                vault_k8s_hub_token_path = os.getenv("HUB_K8S_SA_TOKEN_PATH")
                vault_k8s_auth_role = os.getenv("HUB_VAULT_K8S_AUTH_ROLE")
                vault_k8s_auth_mount = os.getenv("VAULT_K8S_AUTH_MOUNT")

                print("Trying authenticating vault via sa...")

                if vault_k8s_auth_role:
                    for token_path in [vault_k8s_hub_token_path]:
                        try:
                            if os.path.isfile(token_path):
                                with open(token_path, "r") as f:
                                    client.auth.kubernetes.login(
                                        role=vault_k8s_auth_role, jwt=f.read().strip(), mount_point=vault_k8s_auth_mount
                                    )
                                    if client.is_authenticated():
                                        print(f"Authenticated vault using service-account")
                                        return client
                        except Exception as e:
                            raise Exception(f"Vault Auth failed for {token_path}: {str(e)}")

                return client

            class DynamicKubeSpawner(KubeSpawner):

                class NetworkBlockingInitContainerConstructor():
                    def __init__(self, spawner: KubeSpawner):
                        self.spawner = spawner
                        self.vault_client = None

                    def _get_username(self):
                        username = self.spawner.user.name
                        return username
                    
                    def _get_vault_client(self):
                        if not self.vault_client:
                            vault_client = hvac.Client(os.getenv('VAULT_URL'))
                            vault_client.token = os.getenv('VAULT_JHUB_SERVICE_TOKEN')
                            if not vault_client.is_authenticated():
                                vault_client.auth.github.login(os.getenv('VAULT_TOKEN'))
                            self.vault_client = vault_client
                        return self.vault_client

                    def _get_network_access_data(self):
                        vault_client = self._get_vault_client()
                        network_access_data = vault_client.read("dse/services/jupyterhub/network_access_config")["data"]
                        return network_access_data

                    def _get_rules(self, user_groups_info):
                        blocked_ports = set()
                        blocked_hosts = set()   
                        blocked_host_ports = defaultdict(set)
                        allowed_host_ports = defaultdict(set)
                        
                        for group in user_groups_info: 
                            for port in group.get('blocked_ports', []): 
                                blocked_ports.add(port)
                            for host in group.get('blocked_hosts', []): 
                                blocked_hosts.add(host)
                            for host, ports in group.get('blocked_host_ports', {}).items():
                                for port in ports: 
                                    blocked_host_ports[host].add(port)
                            for host, ports in group.get('allowed_host_ports', {}).items():
                                for port in ports:
                                    allowed_host_ports[host].add(port)

                        blocked_ports = list(blocked_ports)
                        blocked_hosts = list(blocked_hosts)
                        blocked_host_ports = {host: list(ports) for host, ports in blocked_host_ports.items()}
                        allowed_host_ports = {host: list(ports) for host, ports in allowed_host_ports.items()}
                        
                        return (blocked_ports, blocked_hosts, blocked_host_ports, allowed_host_ports)

                    def _get_port_block_cmd(self, port):
                        cmd = ["iptables", "-A", "OUTPUT", "-p", "tcp", "--dport", str(port), "-j", "DROP"]
                        return cmd

                    def _get_all_port_cmds(self, ports):
                        cmds = []
                        for port in ports:
                            cmds.append(self._get_port_block_cmd(port))
                        return cmds

                    def _is_ip(self, host):
                        try:
                            socket.inet_aton(host)  # Try to parse as IPv4
                            return True
                        except socket.error:
                            try:
                                socket.inet_pton(socket.AF_INET6, host)  # Try to parse as IPv6
                                return True
                            except socket.error:
                                return False

                    def _resolve_ip(self, domain):
                        try:
                            ip = socket.gethostbyname(domain)
                            print(f"Resolved {domain} to IP: {ip}")
                            return ip
                        except socket.gaierror as e:
                            print(f"Failed to resolve domain {domain}: {e}")
                        return None

                    def _get_ip_block_cmd(self, ip):
                        cmd = ["iptables", "-A", "OUTPUT", "-d", ip, "-j", "DROP"]
                        return cmd

                    def _get_all_host_cmds(self, hosts):
                        cmds = []
                        for host in hosts:
                            if self._is_ip(host):
                                ip = host
                            else:
                                ip = self._resolve_ip(host)
                                if not ip:
                                    continue                
                            cmds.append(self._get_ip_block_cmd(ip))
                            
                        return cmds

                    def _get_all_host_port_rules(self, host_port_rules, allowed_host_ports):
                        cmds = []
                        for host, blocked_ports in host_port_rules.items():
                            if self._is_ip(host):
                                ip = host
                            else:
                                ip = self._resolve_ip(host)
                                if not ip:
                                    continue
                            allowed_ports = allowed_host_ports.get(host, [])
                            for port in blocked_ports:
                                if port in allowed_ports:
                                    continue
                                cmd = ["iptables", "-A", "OUTPUT", "-p", "tcp", "-d", ip, "--dport", str(port), "-j", "DROP"]
                                cmds.append(cmd)
                                
                        return cmds

                    def _get_allowed_host_port_rules(self, allowed_host_ports):
                        cmds = []
                        for host, ports in allowed_host_ports.items():
                            if self._is_ip(host):
                                ip = host
                            else:
                                ip = self._resolve_ip(host)
                                if not ip:
                                    continue
                            for port in ports:
                                cmd = ["iptables", "-I", "OUTPUT", "1", "-p", "tcp", "-d", ip, "--dport", str(port), "-j", "ACCEPT"]
                                cmds.append(cmd)
                        return cmds

                    def _get_blocking_script(self, network_access_data, username):
                        access_group_mapping = network_access_data['groups']
                        groups_data = {}
                        for group in access_group_mapping:
                            groups_data[group['name']] = group
                        user_group_mapping = network_access_data['group_users']
                        
                        current_user_groups = []
                        for group, members in user_group_mapping.items():
                            for member in members:
                                if member == username:
                                    current_user_groups.append(group)

                        if len(current_user_groups) == 0:
                            current_user_groups.append("__default__")

                        current_user_network_access_mapping = [groups_data[group] for group in current_user_groups]
                        (blocked_ports, blocked_hosts, blocked_host_ports, allowed_host_ports) = self._get_rules(current_user_network_access_mapping)

                        cmds = []
                        
                        # First adding the ACCEPT rules for allowed host-port pairs (these need to be processed BEFORE the blocking rules)
                        cmds.extend(self._get_allowed_host_port_rules(allowed_host_ports))
                        
                        # Then adding the blocking rules
                        cmds.extend(self._get_all_port_cmds(blocked_ports))
                        cmds.extend(self._get_all_host_cmds(blocked_hosts))
                        cmds.extend(self._get_all_host_port_rules(blocked_host_ports, allowed_host_ports))

                        command_script = ["sh", "-c", " && ".join(" ".join(cmd) for cmd in cmds)]
                        return command_script

                    def get_ip_block_container(self):
                        network_access_data = self._get_network_access_data()
                        username = self._get_username()

                        blocking_script = self._get_blocking_script(network_access_data, username)

                        network_tools_image_name = os.getenv('NETWORK_TOOLS_IMAGE_NAME')
                        network_tools_image_tag = os.getenv('NETWORK_TOOLS_IMAGE_TAG')

                        ip_block_container = client.V1Container(
                            name="block-host-port-container",
                            image=f"{network_tools_image_name}:{network_tools_image_tag}",
                            command=blocking_script,
                            security_context=client.V1SecurityContext(
                                privileged=True,
                                run_as_user=0,
                                capabilities=client.V1Capabilities(add=["NET_ADMIN"]),
                            ),
                        )
                        return ip_block_container


                def __init__(self, *args, **kwargs):
                    super().__init__(*args, **kwargs)
                    network_blocking_init_container_constructor = DynamicKubeSpawner.NetworkBlockingInitContainerConstructor(self)
                    network_blocking_init_container = network_blocking_init_container_constructor.get_ip_block_container()
                    # self.init_containers.append(network_blocking_init_container)

                profile_form_template = """
                {% raw %}
                <script>
                // JupyterHub 0.8 applied form-control indisciminately to all form elements.
                // Can be removed once we stop supporting JupyterHub 0.8
                $(document).ready(function() {
                    $('#kubespawner-profiles-list input[type="radio"]').removeClass('form-control');

                    function updateOptions() {
                        var selected = $("input[name='profile']:checked")[0].getAttribute("profile");
                        
                        // Handle GPU-specific UI
                        if (selected === 'gpu_environment') {
                            $('#gpu-instance-section').show();
                            $('#gpu-section').show();
                            updateGpuOptions();
                        } else {
                            $('#gpu-instance-section').hide();
                            $('#gpu-section').hide();
                            // Update CPU and Memory options for non-GPU profiles
                            $mem.html($memOptions.filter('[profile="' + selected + '"]'));
                            $cpu.html($cpuOptions.filter('[profile="' + selected + '"]'));
                        }
                    }

                    function updateGpuOptions() {
                        var instance = $('#gpu_instance').val();
                        var instanceData = gpuInstances[instance];
                        
                        if (instanceData) {
                            // Update GPU options
                            $('#gpu').html(instanceData.gpu_options.map(function(gpu) {
                                return '<option value="' + gpu + '">' + gpu + ' GPU' + (gpu > 1 ? 's' : '') + '</option>';
                            }).join(''));
                            
                            // Update CPU options
                            $('#cpu').html(instanceData.cpu_options.map(function(cpu) {
                                return '<option profile="gpu_environment" value="' + cpu + '">' + cpu + ' CPU' + (cpu > 1 ? 's' : '') + '</option>';
                            }).join(''));
                            
                            // Update Memory options
                            $('#mem').html(instanceData.mem_options.map(function(mem) {
                                return '<option profile="gpu_environment" value="' + mem + 'G">' + mem + ' GB</option>';
                            }).join(''));
                            
                            // Update cost display
                            $('#gpu-cost').text(instanceData.cost);
                        }
                    }

                    var $profile = $('#kubespawner-profiles-list');
                    var $mem = $('#mem');
                    var $memOptions = $mem.find('option');
                    var $cpu = $('#cpu');
                    var $cpuOptions = $cpu.find('option');
                    var gpuInstances = {};

                    // Initialize GPU instances data
                    {% for instance in gpu_instances %}
                    gpuInstances['{{ instance.name }}'] = {
                        display_name: '{{ instance.display_name }}',
                        gpu_options: {{ instance.gpu_options }},
                        cpu_options: {{ instance.cpu_options }},
                        mem_options: {{ instance.mem_options }},
                        cost: '{{ instance.cost }}'
                    };
                    {% endfor %}

                    $profile.on('change', updateOptions);
                    $('#gpu_instance').on('change', updateGpuOptions);
                    
                    // Initialize the form
                    updateOptions();
                });
                </script>
                <style>
                /* The profile description should not be bold, even though it is inside the <label> tag */
                #kubespawner-profiles-list label p {
                    font-weight: normal;
                }
                #gpu-instance-section, #gpu-section {
                    display: none;
                }
                .cost-display {
                    font-weight: bold;
                    margin: 10px 0;
                }
                </style>


                <div class='form-group' id='kubespawner-profiles-list'>
                <div>
                <p><b>In our continuous efforts towards making the Blinkit Data Platform secure, we have made some changes to how data is accessed on Jupyterhub</b></p>
                <p>If you find that you do not have access to the required data sources on Jhub please file a request using the following form <a target="_blank" href="https://forms.gle/fZYo5igBDHRt5B48A">https://forms.gle/fZYo5igBDHRt5B48A</a></p>
                <p>If you have a business critical requirement and need the access immediately, please reach out to #bl-data-support on slack and we will process your request</p>
                <p>If you have any other issues please reach out to Data Platform Team over slack or <NAME_EMAIL></p>
                </div>
                {% for profile in profile_list %}
                <label for='profile-item-{{ loop.index0 }}' class='form-control input-group'>
                    <div class='col-md-1'>
                        <input type='radio' name='profile' profile='{{ profile.name }}' id='profile-item-{{ loop.index0 }}' value='{{ loop.index0 }}' {% if profile.default %}checked{% endif %} />
                    </div>
                    <div class='col-md-11'>
                        <strong>{{ profile.display_name }}</strong>
                        {% if profile.description %}
                        <p>{{ profile.description }}</p>
                        {% endif %}
                    </div>
                </label>
                {% endfor %}
                </div>

                <div id="gpu-instance-section" class="form-group">
                    <label for="gpu_instance">GPU Instance Type</label>
                    <select id="gpu_instance" class="form-control" name="gpu_instance">
                        {% for instance in gpu_instances %}
                        <option value="{{ instance.name }}">{{ instance.display_name }} ({{ instance.cost }})</option>
                        {% endfor %}
                    </select>
                    <div class="cost-display">Estimated cost: <span id="gpu-cost"></span></div>
                </div>

                <div id="gpu-section" class="form-group">
                    <label for="gpu">GPUs</label>
                    <select id="gpu" class="form-control" name="gpu"></select>
                </div>

                <div class="form-group">
                    <label for="cpu">CPU</label>
                    <select id="cpu" class="form-control" name="cpu">
                        {% for cpu_option in cpu_options %}
                        <option profile="{{ cpu_option.profile_name }}" value="{{ cpu_option.value }}"> {{ cpu_option.name }} </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group">
                    <label for="mem">Memory</label>
                    <select id="mem" class="form-control" name="mem">
                        {% for mem_option in mem_options %}
                            <option profile="{{ mem_option.profile_name }}" value="{{ mem_option.value }}"> {{ mem_option.name }} </option>
                        {% endfor %}
                    </select>
                </div>

                {% endraw %}
                """

                def get_user_form_options(self, user_name, profile_list):
                    vault_client = hvac.Client(os.getenv('VAULT_URL'))
                    vault_client.token = os.getenv('VAULT_JHUB_SERVICE_TOKEN')
                    if not vault_client.is_authenticated():
                        vault_client.auth.github.login(os.getenv('VAULT_TOKEN'))

                    self.users = vault_client.read("dse/services/jupyterhub/spawner_config")["data"]["users"]
                    self.groups = vault_client.read("dse/services/jupyterhub/spawner_config")["data"]["groups"]
                    self.group_users = vault_client.read("dse/services/jupyterhub/spawner_config")["data"]["group_users"]

                    users = {}
                    for user in self.users:
                        user.get('groups', []).append("__default__")
                        users[user['name']] = user

                    groups = {}
                    for group in self.groups:
                        groups[group['name']] = group

                    for group, members in self.group_users.items():
                        for name in members:
                            _user = users.get(name, {"name": name, "groups": ["__default__"]})
                            _user['groups'].append(group)
                            users[name] = _user

                    user = users.get(user_name, {})
                    groups = [groups[group] for group in user.get('groups', ['__default__'])]
                    allowed_profiles = defaultdict(lambda: defaultdict(int))

                    for profile in user.get('profiles', []):
                        max_mem = profile.get('max_mem', 1)
                        max_cpu = profile.get('max_cpu', 1)
                        if allowed_profiles[profile['name']]['max_mem'] < max_mem:
                            allowed_profiles[profile['name']]['max_mem'] = max_mem

                        if allowed_profiles[profile['name']]['max_cpu'] < max_cpu:
                            allowed_profiles[profile['name']]['max_cpu'] = max_cpu

                    for group in groups:
                        for profile in group['profiles']:
                            max_mem = profile.get('max_mem', 1)
                            max_cpu = profile.get('max_cpu', 1)
                            if allowed_profiles[profile['name']]['max_mem'] < max_mem:
                                allowed_profiles[profile['name']]['max_mem'] = max_mem

                            if allowed_profiles[profile['name']]['max_cpu'] < max_cpu:
                                allowed_profiles[profile['name']]['max_cpu'] = max_cpu

                    profile_options = []
                    for profile in profile_list:
                        pname = profile['extra_config']['name']
                        
                        if pname not in allowed_profiles:
                            continue

                        config = allowed_profiles[pname]
                        
                        if pname == 'gpu_environment':
                            # For GPU environment, we'll use the instance-specific options
                            profile['gpu_instances'] = profile['extra_config']['instance_types']
                            profile['cpu_options'] = []
                            profile['mem_options'] = []
                        else:
                            # For non-GPU profiles
                            mem_options = profile['extra_config']['mem_options']
                            cpu_options = profile['extra_config']['cpu_options']
                            
                            mem_options = list(filter(lambda x: x <= config['max_mem'], mem_options))
                            cpu_options = list(filter(lambda x: x <= config['max_cpu'], cpu_options))
                            
                            profile['cpu_options'] = [{
                                "profile_name": pname,
                                "value": x,
                                "name": str(x)
                            } for x in cpu_options]

                            profile['mem_options'] = [{
                                "profile_name": pname,
                                "value": f"{x}G",
                                "name": f"{x}G"
                            } for x in mem_options]

                        if 'slug' not in profile:
                            profile['slug'] = slugify(profile['display_name'])
                            # to spin up server, need the slug name along with the display name
                            profile['display_name'] = slugify(profile['display_name'])
                        
                        profile['name'] = pname
                        profile_options.append(profile)
                    
                    return profile_options

                def _render_options_form(self, profile_list):
                    self._profile_list = self.get_user_form_options(self.user.name, profile_list)

                    mem_options = []
                    cpu_options = []
                    gpu_instances = []
                    
                    for profile in self._profile_list:
                        if profile['slug'] == 'gpu-environment':
                            gpu_instances = profile.get('gpu_instances', [])
                        else:
                            mem_options.extend(profile['mem_options'])
                            cpu_options.extend(profile['cpu_options'])

                    profile_form_template = Environment(loader=BaseLoader).from_string(self.profile_form_template)
                    return profile_form_template.render(
                        profile_list=self._profile_list,
                        username=self.user.name,
                        mem_options=mem_options,
                        cpu_options=cpu_options,
                        gpu_instances=gpu_instances
                    )

                def options_from_form(self, formdata):
                    try:
                        selected_profile = int(formdata.get('profile', [0])[0])
                        options = self._profile_list[selected_profile]

                        if options['slug'] == 'gpu-environment':
                            # GPU environment specific options
                            gpu_instance = formdata.get('gpu_instance', ['g4dn.xlarge'])[0]
                            gpu_count = int(formdata.get('gpu', [1])[0])
                            cpu_limit = float(formdata.get('cpu', [1])[0])
                            mem_limit = formdata.get('mem', ['8G'])[0]
                            
                            # Fix memory guarantee calculation
                            mem_value = int(mem_limit.strip('G'))
                            mem_guarantee = f"{max(1, mem_value // 2)}G"
                            
                            return {
                                'profile': options['display_name'],
                                'selected_profile': selected_profile,
                                'gpu_instance': gpu_instance,
                                'gpu_count': gpu_count,
                                'cpu_limit': cpu_limit,
                                'cpu_guarantee': min(cpu_limit, 1),
                                'mem_limit': mem_limit,
                                'mem_guarantee': mem_guarantee,
                                'is_gpu': True
                            }
                        else:
                            # Non-GPU environment options
                            cpu_limit = float(formdata.get('cpu', [1])[0])
                            cpu_guarantee = min(float(formdata.get('cpu', [0.5])[0]), 1)
                            mem_limit = formdata.get('mem', ['8G'])[0]
                            
                            # Fix memory guarantee calculation
                            mem_value = int(mem_limit.strip('G'))
                            mem_guarantee = f"{max(1, mem_value // 2)}G"

                            return {
                                'profile': options['display_name'],
                                'selected_profile': selected_profile,
                                'cpu_limit': cpu_limit,
                                'cpu_guarantee': cpu_guarantee,
                                'mem_limit': mem_limit,
                                'mem_guarantee': mem_guarantee,
                                'is_gpu': False
                            }
                    except (TypeError, IndexError, ValueError, KeyError) as e:
                        raise web.HTTPError(400, f"Invalid form data: {str(e)}")

                def modify_pod_hook(self, spawner, pod):
                    units = {'K': 1024, 'M': 1024 ** 2, 'G': 1024 ** 3, 'T': 1024 ** 4}
                    profile = self._profile_list[self.user_options['selected_profile']]
                    
                    # Apply common pod settings
                    pod.spec.containers[0].image = profile['kubespawner_override']['image']
                    pod.spec.containers[0].lifecycle = profile['kubespawner_override']['lifecycle_hooks']
                    pod.spec.containers[0].resources.limits['cpu'] = self.user_options['cpu_limit']
                    pod.spec.containers[0].resources.limits['memory'] = self.user_options['mem_limit']
                    pod.spec.containers[0].resources.requests['cpu'] = self.user_options['cpu_guarantee']
                    pod.spec.containers[0].resources.requests['memory'] = self.user_options['mem_guarantee']
                    pod.spec.service_account_name = profile.get('serviceAccountName', 'default')
                    pod.spec.service_account = profile.get('serviceAccountName', 'default')

                    # Set MEM_LIMIT environment variable
                    for env in pod.spec.containers[0].env:
                        if env.name == 'MEM_LIMIT':
                            mem_limit_str = self.user_options['mem_limit']
                            mem_limit_value, mem_limit_unit = mem_limit_str[:-1], mem_limit_str[-1]
                            env.value = str(int(float(mem_limit_value) * units.get(mem_limit_unit, 1)))
                    
                    # Handle Vault token and policies
                    vault_client = hvac.Client(os.getenv('VAULT_URL'))
                    vault_client.token = os.getenv('VAULT_JHUB_SERVICE_TOKEN')
                    if not vault_client.is_authenticated():
                        vault_client.auth.github.login(os.getenv('VAULT_TOKEN'))
                    
                    groups = vault_client.read("dse/services/jupyterhub/group_config")["data"]["groups"]

                    group_policies = defaultdict(list)
                    for group in groups:
                        group_policies[group["name"]].extend(group["policies"])

                    user_policies = defaultdict(list)
                    for group in groups:
                        for user in group["users"]:
                            user_policies[user].extend(group['policies'])

                    policies = list(user_policies[self.user.name])

                    match = EMAIL_RE.match(self.user.name)
                    if match and match.groups()[1] == 'grofers':
                        policies.append(match.groups()[0])
                        policies.extend(group_policies['jhub-default-grofers'])
                    elif match and match.groups()[1] == 'zomato':
                        policies.append(match.groups()[0] + '_zomato')
                        policies.extend(group_policies['jhub-default-zomato'])

                    for env in pod.spec.containers[0].env:
                        if env.name == 'VAULT_SERVICE_TOKEN':
                            token = vault_client.create_token(policies=policies, renewable=False, ttl="7d")
                            env.value = token["auth"]["client_token"]

                    # Apply profile-specific tolerations and node selectors
                    profile_tolerations = profile.get('extra_config', {}).get('tolerations', [])
                    if profile_tolerations:
                        pod.spec.tolerations.extend(profile_tolerations)
                    
                    profile_node_selector = profile.get('extra_config', {}).get('nodeSelector', {})
                    if profile_node_selector:
                        if not pod.spec.node_selector:
                            pod.spec.node_selector = {}
                        pod.spec.node_selector.update(profile_node_selector)

                    # Handle download restrictions
                    download_flag = 'true'
                    try:
                        apply_restriction = vault_client.read("dse/services/jupyterhub/download_access")["data"]["apply_restriction"]
                        if apply_restriction == "true": 
                            download_access = vault_client.read("dse/services/jupyterhub/download_access")["data"]["users"]
                            if spawner.user.name not in download_access:
                                download_flag = 'false'
                    except Exception as e:
                        print(f'Error fetching download access config: {e}')
                    
                    pod.spec.containers[0].env.append(
                        client.V1EnvVar(name='JUPYTER_DOWNLOAD_ENABLED', value=download_flag)
                    )

                    # Handle GPU-specific configuration
                    if self.user_options.get('is_gpu', False):
                        # Set GPU resources
                        if not pod.spec.containers[0].resources:
                            pod.spec.containers[0].resources = client.V1ResourceRequirements()
                        if not pod.spec.containers[0].resources.limits:
                            pod.spec.containers[0].resources.limits = {}
                            
                        pod.spec.containers[0].resources.requests['nvidia.com/gpu'] = str(self.user_options['gpu_count'])
                        pod.spec.containers[0].resources.limits['nvidia.com/gpu'] = str(self.user_options['gpu_count'])
                        
                        # For GPU instances, set CPU and memory requests to half of their limits
                        cpu_limit = float(self.user_options['cpu_limit'])
                        pod.spec.containers[0].resources.requests['cpu'] = str(max(1, cpu_limit / 2))
                        
                        # Also update memory requests to be half of memory limits
                        mem_limit_str = self.user_options['mem_limit']
                        mem_value = int(mem_limit_str.strip('G'))
                        pod.spec.containers[0].resources.requests['memory'] = f"{max(1, mem_value // 2)}G"
                        
                        # Set instance-specific node selector
                        if not pod.spec.node_selector:
                            pod.spec.node_selector = {}
                        pod.spec.node_selector.update({
                            'node.kubernetes.io/instance-type': self.user_options['gpu_instance']
                        })
                        
                        # Configure shared memory size to 64MB
                        if not hasattr(pod.spec, 'volumes'):
                            pod.spec.volumes = []
                        # Add emptyDir volume with size limit for /dev/shm
                        shm_volume = client.V1Volume(
                            name="dshm",
                            empty_dir=client.V1EmptyDirVolumeSource(
                                medium="Memory",
                                size_limit="8Gi"
                            )
                        )
                        pod.spec.volumes.append(shm_volume)        # Mount the volume to /dev/shm
                        if not hasattr(pod.spec.containers[0], 'volume_mounts'):
                            pod.spec.containers[0].volume_mounts = []
                        shm_mount = client.V1VolumeMount(
                            name="dshm",
                            mount_path="/dev/shm"
                        )
                        pod.spec.containers[0].volume_mounts.append(shm_mount)

                    return pod
            c.JupyterHub.spawner_class = DynamicKubeSpawner
            
proxy:
    secretToken: "{{ secret_token }}"
    https:
        enabled: true
        type: offload
    service:
        annotations:
            service.beta.kubernetes.io/aws-load-balancer-internal: "true"
            service.beta.kubernetes.io/aws-load-balancer-backend-protocol: tcp
            service.beta.kubernetes.io/aws-load-balancer-ssl-ports: https
    chp:
        image:
            name: public.ecr.aws/zomato/jupyterhub/configurable-http-proxy
            tag: "4.5.1"
            pullPolicy: IfNotPresent
        livenessProbe:
            enabled: True
            initialDelaySeconds: 30
            timeoutSeconds: 10
            periodSeconds: 60
        readinessProbe:
            enabled: True
            initialDelaySeconds: 30
            timeoutSeconds: 10
            periodSeconds: 60

singleuser:
    defaultUrl: /lab
    image:
        name: public.ecr.aws/zomato/jupyter/minimal-notebook
        tag: lab-3.2.8
    cpu:
        limit: 1
        guarantee: 0.5
    memory:
        limit: 8G
        guarantee: 1G
    extraPodConfig:
        dnsPolicy: None
        dnsConfig:
            nameservers:
            - ************ # Dnsmasq Service Cluster IP ref: https://z.slack.com/archives/C07R7MAKTK3/p1738058387425939
    extraEnv:
        CHOWN_HOME: "yes"
        VAULT_TOKEN: "{{ github_token }}"
        VAULT_SERVICE_TOKEN: "{{ vault_service_token }}"
        GITHUB_TOKEN: "{{ github_token }}"
        SMTP_HOST: "smtp.grofer.io"
        SMTP_PORT: "9267"
        LIVY_ENDPOINT: "{{ livy_endpoint }}"
    uid: 0
    fsGid: 0
    cmd: start-singleuser.sh

prePuller:
    continuous:
        enabled: false

scheduling:
    userScheduler:
        enabled: false

cull:
    enabled: true
    timeout: 3600
    every: 600
    users: true