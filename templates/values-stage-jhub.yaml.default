hub:
    config:
        GoogleOAuthenticator:
            oauth_callback_url: https://jhub-stage.grofer.io/hub/oauth_callback
    extraConfig:
        jupyter_config: |
            c.Spawner.cmd = ['jupyter-labhub']
            c.Spawner.args = ['--allow-root', '--ResourceUseDisplay.mem_warning_threshold=0.1']
            c.DynamicKubeSpawner.image_pull_policy = 'Always'
            c.DynamicKubeSpawner.volumes = [{
                'name': 'efs',
                'persistentVolumeClaim': {
                    'claimName': 'jhub-efs-encrypted'
                }
            }]
            c.DynamicKubeSpawner.volume_mounts = [{
                'mountPath': '/home/<USER>',
                'name': 'efs',
                'subPath': 'home/{username}'
            }, {
                'mountPath': '/shared',
                'name': 'efs',
                'subPath': 'shared'
            }]
    extraEnv:
        VAULT_URL: https://vault-ui-stage.grofer.io

proxy:
    service:
        annotations:
            service.beta.kubernetes.io/aws-load-balancer-ssl-cert: arn:aws:acm:us-west-2:050634864816:certificate/fb67ea2d-9c58-4f22-89a9-37faebfbba17
            service.beta.kubernetes.io/aws-load-balancer-security-groups: sg-1c4ee565

singleuser:
    profileList: []
    extraEnv:
        VAULT_URL: https://vault-ui-stage.grofer.io

prePuller:
    hook:
        enabled: false
debug:
    enabled: true
