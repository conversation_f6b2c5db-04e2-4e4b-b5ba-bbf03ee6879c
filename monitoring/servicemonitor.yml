apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: hub-servicemonitor
  labels:
    app: hub-servicemonitor
    release: prom-op
spec:
  selector:
    matchLabels:
      app: jupyterhub
      component: hub
  namespaceSelector:
    matchNames:
    - jhub
  endpoints:
  - targetPort: 8081
    path: /hub/metrics
    interval: 60s
    honorLabels: true
    bearerTokenFile: /var/run/secrets/kubernetes.io/serviceaccount/jhub-token
