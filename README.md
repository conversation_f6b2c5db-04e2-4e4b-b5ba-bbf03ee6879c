# Deploy JHUB

## Workflow

1. Create a virtual environment of python3 and install the requirements by running the following.
   ```bash
   pip install -r scripts/requirements-dev.txt
   ```
2. Create value file `values.yaml` and `common.yaml`, PersistenceVolume file `pv.yaml`, PersistenceVolumeClaim file `pvc.yaml` in directory `{env}/{namespace}/` and Skaffold file `skaffold.yaml` by running the following.
   ```bash
   python scripts/config-setup.py --env stage
   ```
   where,
   * `--env` is the k8s cluster environment and values can be `stage` or `prod`.

3. Deploy the PersistenceVolume and PersistenceVolumeClaim by running the following.
   > Note: This step is only required if PersistenceVolume and PersistenceVolumeClaim doesn't exist already
   ```bash
   skaffold run -p persistence-volume
   ```

4. Deploy the Jhub by running the following.
   ```bash
   skaffold run -p jhub
   ```
