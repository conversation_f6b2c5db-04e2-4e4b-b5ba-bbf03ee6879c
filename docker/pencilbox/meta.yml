default: true
environments:
  - prod
  - stage
display_order: 0
display_name: Pencilbox environment
description: To have every bell and whistle, along with a pencilbox for your notebooks.
kubespawner_override:
  lifecycle_hooks:
    postStart:
      exec:
        command: ['sh', '/root/post-start.sh']
    preStop:
      exec:
        command: ['sh', '/root/pre-stop.sh']
extra_config:
  name: pencilbox
  cpu_options:
  - 1
  - 2
  - 3
  - 4
  - 6
  - 8
  - 16
  mem_options:
  - 2
  - 4
  - 8
  - 16
  - 32
  - 48
  - 64
  - 128
