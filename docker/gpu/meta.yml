display_order: 1
environments:
  - prod
  - stage
display_name: GPU Environment
description: Environment with GPU support
kubespawner_override:
  lifecycle_hooks:
    postStart:
      exec:
        command: ['sh', '/root/post-start.sh']
    preStop:
      exec:
        command: ['sh', '/root/pre-stop.sh']
extra_config:
  name: gpu_environment
  instance_types:
    - name: g4dn.xlarge
      display_name: g4dn.xlarge
      gpu_options: [1]
      cpu_options: [1,2,4]
      mem_options: [2,4,8,16]
      cost: "$0.214/hour"

    - name: g4dn.2xlarge
      display_name: g4dn.2xlarge
      gpu_options: [1]
      cpu_options: [1,2,4,8]
      mem_options: [2,4,8,16,32]
      cost: "$0.352/hour"

    - name: g4dn.metal
      display_name: g4dn.metal
      gpu_options: [1, 2, 4, 8]
      cpu_options: [24, 48, 72, 96]
      mem_options: [96, 192, 288, 384]
      cost: "$3.582/hour"

    - name: g4dn.12xlarge
      display_name: g4dn.12xlarge
      gpu_options: [1, 2, 4]
      cpu_options: [4, 8, 12, 24, 36, 48]
      mem_options: [16, 32, 48, 64, 96, 128, 192]
      cost: "$1.394/hour"

  nodeSelector:
    nodegroup-name: jhub-gpu-spot
  tolerations:
    - key: team
      value: data-science-gpu
      effect: NoSchedule
      operator: Equal