environments:
  - prod
display_order: 0
display_name: Demand Forecasting Environment
description: Demand Forecasting Environment
kubespawner_override:
  lifecycle_hooks:
    postStart:
      exec:
        command: ['sh', '/root/post-start.sh']
    preStop:
      exec:
        command: ['sh', '/root/pre-stop.sh']
extra_config:
  name: demand_forecasting
  cpu_options:
    - 1
    - 2
    - 3
    - 4
    - 6
    - 8
    - 16
  mem_options:
    - 2
    - 4
    - 8
    - 16
    - 32
    - 48
    - 64
    - 128
  
  nodeSelector:
    nodegroup-name: jhub-ondemand-data-science

  tolerations:
    - key: team
      value: data-science
      effect: NoSchedule
      operator: Equal
