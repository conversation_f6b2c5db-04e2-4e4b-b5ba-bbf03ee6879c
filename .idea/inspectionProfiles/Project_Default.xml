<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyCompatibilityInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ourVersions">
        <value>
          <list size="1">
            <item index="0" class="java.lang.String" itemvalue="3.7" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="13">
            <item index="0" class="java.lang.String" itemvalue="confluent-kafka" />
            <item index="1" class="java.lang.String" itemvalue="statsmodels" />
            <item index="2" class="java.lang.String" itemvalue="fastavro" />
            <item index="3" class="java.lang.String" itemvalue="airflow" />
            <item index="4" class="java.lang.String" itemvalue="gensim" />
            <item index="5" class="java.lang.String" itemvalue="seaborn" />
            <item index="6" class="java.lang.String" itemvalue="googleads" />
            <item index="7" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="8" class="java.lang.String" itemvalue="Shapely" />
            <item index="9" class="java.lang.String" itemvalue="cryptography" />
            <item index="10" class="java.lang.String" itemvalue="numpy" />
            <item index="11" class="java.lang.String" itemvalue="xgboost" />
            <item index="12" class="java.lang.String" itemvalue="lightgbm" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="VulnerableLibrariesLocal" enabled="false" level="WARNING" enabled_by_default="false" />
  </profile>
</component>