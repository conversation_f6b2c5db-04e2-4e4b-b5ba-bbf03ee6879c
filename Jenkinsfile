pipeline {
  agent any
  options {
    buildDiscarder(logRotator(numToKeepStr: '15', daysToKeepStr: '5'))
    disableConcurrentBuilds()
    skipStagesAfterUnstable()
  }

  environment {
    VAULT_TOKEN = credentials("jenkins-github-token")
    VAULT_AUTH_GITHUB_TOKEN = credentials("jenkins-github-token")
    VAULT_URL = "https://vault-ui-prod.grofer.io"
    SLACK_CHANNEL = "#bl-data-deployments"
    RELEASE = "jhub"
    PROJECT = env.JOB_NAME.toLowerCase()
  }

  parameters {
    choice(name: 'environment', choices:['stage', 'prod'], description: 'k8s cluster environment')
  }

  stages {
    stage("Setup the configs for operator/deployment") {
      when { triggeredBy cause: "UserIdCause" }
      agent {
        docker {
          image 'python:3.9.13'
          args '-u root'
        }
      }
      steps {
          withEnv(["VAULT_TOKEN=${env.VAULT_TOKEN}"]) {
            sh(script: "pip install -r scripts/requirements-dev.txt")
            sh(script: "python scripts/config-setup.py --env ${environment}")
            // To keep the yaml files generated in this step and use in the next step
            stash includes: "skaffold.yaml, ${environment}/jhub/*.yaml", name: 'k8s-config'
            sh(script: "rm -rf scripts/__pycache__ skaffold.yaml ${environment}")
          }
        }
    }

    stage("Ready for Jhub deployment") {
      when {
        branch 'master'
        // branch 'code-server-poc'
      }
      steps {
        slackSend botUser: true, tokenCredentialId: "jenkins-app-token", channel: "${SLACK_CHANNEL}", message: "[${PROJECT}]: The build is ready for jHub deployment: ${env.RUN_DISPLAY_URL}"
      }
    }

    stage("Deploying jHub") {
      when {
        beforeInput true
        anyOf {
        branch 'master'
        branch 'code-server-poc'
        expression { params.environment=='stage' }
        }
      }
      input {
        message "Do you want to proceed for jHub deployment?"
      }
      steps {
        unstash 'k8s-config'
        slackSend botUser: true, tokenCredentialId: "jenkins-app-token", channel: "${SLACK_CHANNEL}", message: "[${PROJECT}]: Deploying master. Build: ${env.RUN_DISPLAY_URL}"
        script {
          if (params.environment == 'prod') {
            credentials_id = 'prod-eks-kubeconfig'
          } else {
              credentials_id = 'stage-kubeconfig'
          }
        }
        withKubeConfig([credentialsId:credentials_id]) {
          sh(script: "kubectl config set-context --current --namespace=jhub")
          sh(script: "skaffold run -p service-account")
          // helm backup commands incase there are any issues with skaffold
          // sh(script: "helm repo add jupyterhub https://jupyterhub.github.io/helm-chart/ --insecure-skip-tls-verify")
          // sh(script: "helm search repo -l | grep jupyterhub")
          // sh(script: "helm repo update --insecure-skip-tls-verify")
          // sh(script: "helm upgrade ${RELEASE} jupyterhub/jupyterhub --version 1.2.0 --insecure-skip-tls-verify --namespace jhub --values ${environment}/jhub/common.yaml --values ${environment}/jhub/values.yaml --timeout 3600s --wait")
          sh(script: "skaffold run -p jhub")
        }
      }
    }
  }

  post {
    success {
      script {
        if (env.BRANCH_NAME == "master") {
          slackSend(botUser: true, tokenCredentialId: "jenkins-app-token", color: "good", message: "[${PROJECT}]: pipeline ${currentBuild.fullDisplayName} completed successfully. See ${env.RUN_DISPLAY_URL} for details.", channel: "$SLACK_CHANNEL")
        }
      }
    }

    failure {
      script {
        if (env.BRANCH_NAME == "master") {
          slackSend(botUser: true, tokenCredentialId: "jenkins-app-token", color: "danger", message: "[${PROJECT}]: pipeline ${currentBuild.fullDisplayName} failed. Please contact data on call. See ${env.RUN_DISPLAY_URL} for details.", channel: "#bl-data-alerts-p0")
        }
      }
    }
  }
}
